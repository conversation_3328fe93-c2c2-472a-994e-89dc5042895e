"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/reset-password/page",{

/***/ "(app-pages-browser)/./src/app/auth/reset-password/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/auth/reset-password/page.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ResetPasswordPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiAlertTriangle_FiCheck_FiLoader_FiLock_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlertTriangle,FiCheck,FiLoader,FiLock!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n// ===== Archivo: src/app/auth/reset-password/page.tsx (VERSIÓN FINAL Y ROBUSTA) =====\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ResetPasswordForm() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [confirmPassword, setConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isVerifying, setIsVerifying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [hasRecoverySession, setHasRecoverySession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [linkError, setLinkError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ResetPasswordForm.useEffect\": ()=>{\n            // 1. Verificar errores explícitos en la URL\n            const urlErrorParam = searchParams.get('error_description');\n            if (urlErrorParam) {\n                setLinkError(decodeURIComponent(urlErrorParam));\n                setIsVerifying(false);\n                return;\n            }\n            const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.createClient)();\n            let verificationTimeout = null;\n            // 2. Configurar el listener de autenticación\n            const { data: authListener } = supabase.auth.onAuthStateChange({\n                \"ResetPasswordForm.useEffect\": (event, session)=>{\n                    // Este evento se dispara cuando Supabase procesa el token de la URL\n                    if (event === 'PASSWORD_RECOVERY') {\n                        if (verificationTimeout) clearTimeout(verificationTimeout);\n                        if (session) {\n                            setHasRecoverySession(true);\n                            setIsVerifying(false);\n                            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.success('Sesión de recuperación verificada. Ya puedes crear tu nueva contraseña.');\n                        } else {\n                            setLinkError('El enlace de recuperación es inválido o ha expirado.');\n                            setIsVerifying(false);\n                        }\n                    }\n                }\n            }[\"ResetPasswordForm.useEffect\"]);\n            // 3. Verificar si ya hay una sesión de recuperación activa (si se recarga la página)\n            const checkInitialSession = {\n                \"ResetPasswordForm.useEffect.checkInitialSession\": async ()=>{\n                    const { data: { session } } = await supabase.auth.getSession();\n                    // Un token de recuperación establece una sesión de autenticación normal\n                    if (session === null || session === void 0 ? void 0 : session.user) {\n                        setHasRecoverySession(true);\n                        setIsVerifying(false);\n                    } else {\n                        // Si no hay sesión, iniciar el timeout de seguridad\n                        verificationTimeout = setTimeout({\n                            \"ResetPasswordForm.useEffect.checkInitialSession\": ()=>{\n                                if (isVerifying) {\n                                    setLinkError('No se pudo verificar el enlace. Es posible que haya expirado o ya se haya utilizado.');\n                                    setIsVerifying(false);\n                                }\n                            }\n                        }[\"ResetPasswordForm.useEffect.checkInitialSession\"], 10000); // 10 segundos\n                    }\n                }\n            }[\"ResetPasswordForm.useEffect.checkInitialSession\"];\n            checkInitialSession();\n            // 4. Limpieza\n            return ({\n                \"ResetPasswordForm.useEffect\": ()=>{\n                    authListener === null || authListener === void 0 ? void 0 : authListener.subscription.unsubscribe();\n                    if (verificationTimeout) clearTimeout(verificationTimeout);\n                }\n            })[\"ResetPasswordForm.useEffect\"];\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ResetPasswordForm.useEffect\"], []); // Se ejecuta solo una vez al montar\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError('');\n        if (!hasRecoverySession) {\n            setError('No se ha establecido una sesión válida para cambiar la contraseña. Por favor, utiliza el enlace de tu email de nuevo.');\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Error de sesión. Intenta usar el enlace de tu email de nuevo.');\n            return;\n        }\n        if (password.length < 6) {\n            setError('La nueva contraseña debe tener al menos 6 caracteres.');\n            return;\n        }\n        if (password !== confirmPassword) {\n            setError('Las contraseñas no coinciden.');\n            return;\n        }\n        setLoading(true);\n        const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.createClient)();\n        const { error: updateError } = await supabase.auth.updateUser({\n            password\n        });\n        setLoading(false);\n        if (updateError) {\n            setError(updateError.message === \"Auth session missing!\" ? \"Error de sesión: Tu sesión ha expirado o es inválida. Por favor, usa el enlace de tu email de nuevo.\" : updateError.message);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Error al actualizar la contraseña.');\n        } else {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.success('¡Contraseña establecida exitosamente!');\n            setTimeout(()=>{\n                router.push('/login');\n            }, 1500);\n        }\n    };\n    // ----- RENDERIZADO CONDICIONAL -----\n    // (El JSX no cambia, es el mismo que te proporcioné antes)\n    if (isVerifying) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiCheck_FiLoader_FiLock_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiLoader, {\n                    className: \"w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-gray-800\",\n                    children: \"Verificando enlace...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mt-2\",\n                    children: \"Esto puede tardar unos segundos.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, this);\n    }\n    if (linkError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-red-50 flex flex-col justify-center items-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiCheck_FiLoader_FiLock_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiAlertTriangle, {\n                        className: \"w-12 h-12 text-red-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-800 mb-2\",\n                        children: \"Enlace Inv\\xe1lido o Expirado\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: linkError\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/login'),\n                        className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors\",\n                        children: \"Volver a Inicio de Sesi\\xf3n\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this);\n    }\n    if (!hasRecoverySession) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiCheck_FiLoader_FiLock_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiLock, {\n                    className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-gray-700\",\n                    children: \"Acceso no Autorizado\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500 mt-2 mb-6 max-w-md text-center\",\n                    children: \"Esta p\\xe1gina es para establecer tu contrase\\xf1a usando un enlace seguro. Si necesitas restablecer tu contrase\\xf1a, solic\\xedtalo desde la p\\xe1gina de inicio de sesi\\xf3n.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>router.push('/login'),\n                    className: \"bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors\",\n                    children: \"Ir a Inicio de Sesi\\xf3n\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 10\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n            lineNumber: 149,\n            columnNumber: 8\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiCheck_FiLoader_FiLock_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiLock, {\n                            className: \"w-12 h-12 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n                        children: \"Crea tu Nueva Contrase\\xf1a\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        className: \"space-y-6\",\n                        onSubmit: handleSubmit,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"password\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Nueva Contrase\\xf1a\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"password\",\n                                            name: \"password\",\n                                            type: \"password\",\n                                            required: true,\n                                            value: password,\n                                            onChange: (e)=>setPassword(e.target.value),\n                                            className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            placeholder: \"M\\xednimo 6 caracteres\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"confirmPassword\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Confirmar Nueva Contrase\\xf1a\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"confirmPassword\",\n                                            name: \"confirmPassword\",\n                                            type: \"password\",\n                                            required: true,\n                                            value: confirmPassword,\n                                            onChange: (e)=>setConfirmPassword(e.target.value),\n                                            className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            placeholder: \"Repite la contrase\\xf1a\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-red-500 text-sm bg-red-50 p-3 rounded-md border border-red-200\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiCheck_FiLoader_FiLock_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiLoader, {\n                                                className: \"animate-spin h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 32\n                                            }, this),\n                                            \" Actualizando...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiCheck_FiLoader_FiLock_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiCheck, {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 109\n                                            }, this),\n                                            \" Establecer Contrase\\xf1a\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, this);\n}\n_s(ResetPasswordForm, \"dHnEPFpaNKchdadNyLPWJBsExEA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ResetPasswordForm;\nfunction ResetPasswordPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Cargando...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n            lineNumber: 209,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResetPasswordForm, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n            lineNumber: 210,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n        lineNumber: 209,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ResetPasswordPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ResetPasswordForm\");\n$RefreshReg$(_c1, \"ResetPasswordPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/reset-password/page.tsx\n"));

/***/ })

});