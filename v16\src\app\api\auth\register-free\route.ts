// ===== Archivo: src\app\api\auth\register-free\route.ts =====
// src/app/api/auth/register-free/route.ts
// Endpoint para registro de usuarios gratuitos con contraseña temporal + resetPasswordForEmail

import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase/admin';

// Rate limiting para prevenir spam
const registrationAttempts = new Map<string, { count: number; lastAttempt: number }>();
const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutos
const MAX_ATTEMPTS = 3;

export async function POST(request: NextRequest) {
  const clientIP = request.headers.get('x-forwarded-for') || 'unknown';

  try {
    const { email, customerName } = await request.json();

    // Validación básica
    if (!email) {
      return NextResponse.json(
        { error: 'Email es requerido' },
        { status: 400 }
      );
    }

    // Rate limiting
    const now = Date.now();
    const attempts = registrationAttempts.get(clientIP);

    if (attempts && attempts.count >= MAX_ATTEMPTS && (now - attempts.lastAttempt) < RATE_LIMIT_WINDOW) {
      return NextResponse.json(
        { error: 'Demasiados intentos de registro. Intenta de nuevo en 15 minutos.' },
        { status: 429 }
      );
    }

    // Verificar si el usuario ya existe
    console.log('🔍 [REGISTER-FREE] Verificando si el usuario ya existe...');
    try {
      const { data: existingUsers } = await supabaseAdmin.auth.admin.listUsers();
      const existingUser = existingUsers.users.find(user => user.email === email);
      if (existingUser) {
        console.log('❌ [REGISTER-FREE] Usuario ya existe:', existingUser.id);

        // Actualizar rate limiting
        registrationAttempts.set(clientIP, {
          count: (attempts?.count || 0) + 1,
          lastAttempt: now
        });

        return NextResponse.json({
          error: 'Ya existe una cuenta con este email. Si olvidaste tu contraseña, usa la opción de recuperación.'
        }, { status: 409 });
      }
    } catch (error) {
      // Si el error es "User not found", está bien, podemos continuar
      console.log('✅ [REGISTER-FREE] Usuario no existe, continuando con invitación...');
    }

    // Calcular fecha de expiración (5 días desde ahora)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 5);



    // Preparar metadatos del usuario
    const userData = {
      name: customerName || email.split('@')[0],
      plan: 'free',
      free_account: true,
      expires_at: expiresAt.toISOString(),
      created_via: 'free_registration',
      registration_type: 'invitation_flow',
    };

    console.log('📧 [REGISTER-FREE] Invitando usuario con datos:', {
      email,
      userData,
      redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/confirm-reset`
    });

    // Usar inviteUserByEmail que es más confiable para envío de emails
    const { data, error: createError } = await supabaseAdmin.auth.admin.inviteUserByEmail(
      email,
      {
        data: userData,
        redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/confirm-reset`
      }
    );

    console.log('📊 [REGISTER-FREE] Resultado de inviteUserByEmail:', {
      hasUser: !!data?.user,
      userId: data?.user?.id,
      hasError: !!createError,
      errorMessage: createError?.message
    });

    if (createError) {
      console.error('❌ [REGISTER-FREE] Error invitando usuario:', createError);

      // Actualizar rate limiting
      registrationAttempts.set(clientIP, {
        count: (attempts?.count || 0) + 1,
        lastAttempt: now
      });

      if (createError.message?.includes('User already registered')) {
        return NextResponse.json({
          error: 'Ya existe una cuenta con este email.'
        }, { status: 409 });
      }

      return NextResponse.json({
        error: 'Error invitando usuario. Por favor, intenta de nuevo.'
      }, { status: 500 });
    }

    const user = data.user;

    if (!user) {
      return NextResponse.json({
        error: 'No se pudo crear el usuario.'
      }, { status: 500 });
    }

    // Crear perfil del usuario
    console.log('👤 [REGISTER-FREE] Creando perfil del usuario...');
    try {
      const profileData = {
        user_id: user.id,
        subscription_plan: 'free' as const,
        plan_expires_at: expiresAt.toISOString(),
        payment_verified: false,
        current_month_tokens: 0,
        monthly_token_limit: 50000
      };

      const { error: profileError } = await supabaseAdmin
        .from('user_profiles')
        .insert(profileData);

      if (profileError) {
        console.error('⚠️ [REGISTER-FREE] Error creando perfil:', profileError);
        // No fallar el registro por esto
      } else {
        console.log('✅ [REGISTER-FREE] Perfil creado exitosamente');
      }
    } catch (profileError) {
      console.error('⚠️ [REGISTER-FREE] Error inesperado creando perfil:', profileError);
    }

    console.log('📧 [REGISTER-FREE] Usuario invitado exitosamente');

    // Limpiar rate limiting en caso de éxito
    registrationAttempts.delete(clientIP);

    return NextResponse.json({
      success: true,
      message: '¡Casi listo! Te hemos enviado un email para que confirmes tu cuenta y establezcas tu contraseña.',
      flow: 'confirm_and_setup'
    });

  } catch (error) {
    console.error('Error en registro gratuito:', error);

    // Actualizar rate limiting
    registrationAttempts.set(clientIP, {
      count: (registrationAttempts.get(clientIP)?.count || 0) + 1,
      lastAttempt: Date.now()
    });

    return NextResponse.json(
      { error: 'Error interno del servidor. Por favor, intenta de nuevo.' },
      { status: 500 }
    );
  }
}
