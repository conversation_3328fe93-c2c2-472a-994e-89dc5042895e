// ===== Archivo: src/app/api/auth/register-free/route.ts (FLUJO SIMPLE Y DIRECTO) =====

import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase/admin';

export async function POST(request: NextRequest) {
  try {
    // Ahora sí usamos la contraseña que viene del formulario
    const { email, password, customerName } = await request.json();

    if (!email || !password) {
      return NextResponse.json({ error: 'Email y contraseña son requeridos' }, { status: 400 });
    }
    if (password.length < 6) {
        return NextResponse.json({ error: 'La contraseña debe tener al menos 6 caracteres' }, { status: 400 });
    }

    console.log('--- INICIO REGISTRO ---');

    // 1. Crear el usuario con la contraseña proporcionada por él.
    //    `email_confirm: false` es importante para que se envíe el email de confirmación.
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 5);

    const { data: { user }, error: createError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: false,
      user_metadata: {
        name: customerName || email.split('@')[0],
        plan: 'free',
        free_account: true,
        expires_at: expiresAt.toISOString(),
      }
    });
    console.log('Después de createUser', { user, createError });

    if (createError) {
      console.error('Error en createUser:', createError);
      // Manejar el caso de que el usuario ya exista
      if (createError.message.includes('User already registered')) {
        return NextResponse.json({ error: 'Ya existe una cuenta con este email.' }, { status: 409 });
      }
      throw createError;
    }

    if (!user) {
      console.error('No se pudo crear el usuario.');
      throw new Error("No se pudo crear el usuario.");
    }

    console.log('Antes de generateLink');
    const result = await supabaseAdmin.auth.admin.generateLink({
      type: 'signup',
      email,
      password,
      options: {
        redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/confirmed`
      }
    });
    console.log('Resultado de generateLink:', result);

    if (result.error) {
      console.error('❌ Error enviando email de confirmación:', result.error);
      throw result.error;
    } else {
      console.log('✅ Email de confirmación enviado exitosamente a:', email);
    }

    console.log('✅ Usuario creado, pendiente de confirmación:', user.id);
    
    // 2. Crear el perfil de usuario asociado
    await supabaseAdmin.from('user_profiles').insert({
      user_id: user.id,
      subscription_plan: 'free',
      plan_expires_at: expiresAt.toISOString(),
      payment_verified: true,
      monthly_token_limit: 50000,
      current_month_tokens: 0,
      security_flags: {
        created_via_signup: true,
        free_account: true,
        expires_at: expiresAt.toISOString()
      }
    });
    console.log('✅ Perfil de usuario creado para:', user.id);

    return NextResponse.json({
      success: true,
      message: '¡Registro exitoso! Revisa tu email para confirmar tu cuenta y poder iniciar sesión.',
    });

  } catch (error) {
    console.error('Error en registro gratuito:', error);
    const errorMessage = error instanceof Error ? error.message : 'Error interno del servidor.';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}