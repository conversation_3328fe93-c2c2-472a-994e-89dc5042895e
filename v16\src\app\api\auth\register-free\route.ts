﻿// ===== PEGA ESTE CÓDIGO COMPLETO EN: src/app/api/auth/register-free/route.ts =====

import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase/admin';

// Rate limiting para prevenir spam
const registrationAttempts = new Map<string, { count: number; lastAttempt: number }>();
const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutos
const MAX_ATTEMPTS = 3;

export async function POST(request: NextRequest) {
  const clientIP = request.headers.get('x-forwarded-for') || 'unknown';

  try {
    // Solo esperamos email y customerName, la contraseña no es necesaria aquí.
    const { email, customerName } = await request.json();

    // Validación básica
    if (!email) {
      return NextResponse.json({ error: 'Email es requerido' }, { status: 400 });
    }

    // Rate limiting (se mantiene igual)
    const now = Date.now();
    const attempts = registrationAttempts.get(clientIP);

    if (attempts && attempts.count >= MAX_ATTEMPTS && (now - attempts.lastAttempt) < RATE_LIMIT_WINDOW) {
      return NextResponse.json(
        { error: 'Demasiados intentos de registro. Intenta de nuevo en 15 minutos.' },
        { status: 429 }
      );
    }

    // Verificar si el usuario ya existe
    const { data: { users }, error: listError } = await supabaseAdmin.auth.admin.listUsers();
    if (listError) {
        throw new Error("Error verificando usuario existente.");
    }

    const exactUser = users.find(u => u.email === email);
    if (exactUser) {
      registrationAttempts.set(clientIP, { count: (attempts?.count || 0) + 1, lastAttempt: now });
      return NextResponse.json({
        error: 'Ya existe una cuenta con este email. Si olvidaste tu contraseña, usa la opción de recuperación.'
      }, { status: 409 });
    }

    // Calcular fecha de expiración y preparar metadatos
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 5);

    const userData = {
      name: customerName || email.split('@')[0],
      plan: 'free',
      free_account: true,
      expires_at: expiresAt.toISOString(),
      created_via: 'free_registration_invitation'
    };

    // Invitar al usuario. Esto crea el usuario y envía un enlace de invitación.
    const { data: inviteData, error: inviteError } = await supabaseAdmin.auth.admin.inviteUserByEmail(
      email,
      {
        data: userData,
        redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/reset-password`
      }
    );

    if (inviteError) {
      throw inviteError;
    }

    if (!inviteData.user) {
        throw new Error("La invitación no devolvió un usuario.");
    }

    const newUser = inviteData.user;

    // Crear el perfil de usuario asociado
    await supabaseAdmin.from('user_profiles').insert({
      user_id: newUser.id,
      subscription_plan: 'free',
      plan_expires_at: expiresAt.toISOString(),
      payment_verified: true,
      monthly_token_limit: 50000,
      current_month_tokens: 0,
      security_flags: {
        created_via_free_invitation: true,
        free_account: true,
        expires_at: expiresAt.toISOString()
      }
    });

    registrationAttempts.delete(clientIP);

    return NextResponse.json({
      success: true,
      message: 'Te hemos enviado un enlace a tu email para establecer tu contraseña y activar tu cuenta.',
    });

  } catch (error) {
    console.error('Error en registro gratuito:', error);
    registrationAttempts.set(clientIP, { count: (registrationAttempts.get(clientIP)?.count || 0) + 1, lastAttempt: Date.now() });
    const errorMessage = error instanceof Error ? error.message : 'Error interno del servidor.';
    if (errorMessage.includes('User already registered')) {
        return NextResponse.json({ error: 'Ya existe una cuenta con este email.' }, { status: 409 });
    }
    return NextResponse.json({ error: 'Error interno del servidor. Por favor, intenta de nuevo.' }, { status: 500 });
  }
}
