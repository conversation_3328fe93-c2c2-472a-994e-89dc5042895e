/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/register-free/route";
exports.ids = ["app/api/auth/register-free/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister-free%2Froute&page=%2Fapi%2Fauth%2Fregister-free%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister-free%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister-free%2Froute&page=%2Fapi%2Fauth%2Fregister-free%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister-free%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_src_app_api_auth_register_free_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/register-free/route.ts */ \"(rsc)/./src/app/api/auth/register-free/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/register-free/route\",\n        pathname: \"/api/auth/register-free\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/register-free/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\api\\\\auth\\\\register-free\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_naata_Documents_augment_projects_OposI_v16_src_app_api_auth_register_free_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZhdXRoJTJGcmVnaXN0ZXItZnJlZSUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGYXV0aCUyRnJlZ2lzdGVyLWZyZWUlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZhdXRoJTJGcmVnaXN0ZXItZnJlZSUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNuYWF0YSU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUNPcG9zSSU1Q3YxNiU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDbmFhdGElNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDT3Bvc0klNUN2MTYmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQStGO0FBQ3ZDO0FBQ3FCO0FBQ3VEO0FBQ3BJO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix5R0FBbUI7QUFDM0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsc0RBQXNEO0FBQzlEO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQzBGOztBQUUxRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxuYWF0YVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxPcG9zSVxcXFx2MTZcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcYXV0aFxcXFxyZWdpc3Rlci1mcmVlXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9hdXRoL3JlZ2lzdGVyLWZyZWUvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9hdXRoL3JlZ2lzdGVyLWZyZWVcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL2F1dGgvcmVnaXN0ZXItZnJlZS9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkM6XFxcXFVzZXJzXFxcXG5hYXRhXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXE9wb3NJXFxcXHYxNlxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxhdXRoXFxcXHJlZ2lzdGVyLWZyZWVcXFxccm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICB3b3JrQXN5bmNTdG9yYWdlLFxuICAgICAgICB3b3JrVW5pdEFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister-free%2Froute&page=%2Fapi%2Fauth%2Fregister-free%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister-free%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/register-free/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/register-free/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/admin */ \"(rsc)/./src/lib/supabase/admin.ts\");\n// ===== Archivo: src\\app\\api\\auth\\register-free\\route.ts =====\n// src/app/api/auth/register-free/route.ts\n// Endpoint para registro de usuarios gratuitos con inviteUserByEmail\n\n\n// Rate limiting para prevenir spam\nconst registrationAttempts = new Map();\nconst RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutos\nconst MAX_ATTEMPTS = 3;\nasync function POST(request) {\n    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';\n    try {\n        const { email, password, customerName } = await request.json();\n        // Validación básica\n        if (!email || !password) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Email y contraseña son requeridos'\n            }, {\n                status: 400\n            });\n        }\n        // Rate limiting\n        const now = Date.now();\n        const attempts = registrationAttempts.get(clientIP);\n        if (attempts && attempts.count >= MAX_ATTEMPTS && now - attempts.lastAttempt < RATE_LIMIT_WINDOW) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Demasiados intentos de registro. Intenta de nuevo en 15 minutos.'\n            }, {\n                status: 429\n            });\n        }\n        // Verificar si el usuario ya existe\n        console.log('🔍 [REGISTER-FREE] Verificando si el usuario ya existe...');\n        try {\n            const { data: existingUsers } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.auth.admin.listUsers();\n            const existingUser = existingUsers.users.find((user)=>user.email === email);\n            if (existingUser) {\n                console.log('❌ [REGISTER-FREE] Usuario ya existe:', existingUser.id);\n                // Actualizar rate limiting\n                registrationAttempts.set(clientIP, {\n                    count: (attempts?.count || 0) + 1,\n                    lastAttempt: now\n                });\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Ya existe una cuenta con este email. Si olvidaste tu contraseña, usa la opción de recuperación.'\n                }, {\n                    status: 409\n                });\n            }\n        } catch (error) {\n            // Si el error es \"User not found\", está bien, podemos continuar\n            console.log('✅ [REGISTER-FREE] Usuario no existe, continuando con invitación...');\n        }\n        // Calcular fecha de expiración (5 días desde ahora)\n        const expiresAt = new Date();\n        expiresAt.setDate(expiresAt.getDate() + 5);\n        // Preparar metadatos del usuario\n        const userData = {\n            name: customerName || email.split('@')[0],\n            plan: 'free',\n            free_account: true,\n            expires_at: expiresAt.toISOString(),\n            created_via: 'free_registration',\n            registration_type: 'free_invitation_flow'\n        };\n        console.log('📧 [REGISTER-FREE] Invitando usuario con datos:', {\n            email,\n            userData,\n            redirectTo: `${\"http://localhost:3001\"}/auth/reset-password`\n        });\n        // Usar inviteUserByEmail para el flujo correcto de registro gratuito\n        const { data, error: inviteError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.auth.admin.inviteUserByEmail(email, {\n            data: userData,\n            redirectTo: `${\"http://localhost:3001\"}/auth/reset-password`\n        });\n        console.log('📊 [REGISTER-FREE] Resultado de inviteUserByEmail:', {\n            hasUser: !!data?.user,\n            userId: data?.user?.id,\n            hasError: !!inviteError,\n            errorMessage: inviteError?.message\n        });\n        if (inviteError) {\n            console.error('❌ [REGISTER-FREE] Error invitando usuario:', inviteError);\n            // Actualizar rate limiting\n            registrationAttempts.set(clientIP, {\n                count: (attempts?.count || 0) + 1,\n                lastAttempt: now\n            });\n            if (inviteError.message?.includes('User already registered')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Ya existe una cuenta con este email.'\n                }, {\n                    status: 409\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Error enviando invitación. Por favor, intenta de nuevo.'\n            }, {\n                status: 500\n            });\n        }\n        const user = data.user;\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No se pudo crear el usuario.'\n            }, {\n                status: 500\n            });\n        }\n        // Crear perfil del usuario\n        console.log('👤 [REGISTER-FREE] Creando perfil del usuario...');\n        try {\n            const profileData = {\n                user_id: user.id,\n                subscription_plan: 'free',\n                plan_expires_at: expiresAt.toISOString(),\n                payment_verified: false,\n                current_month_tokens: 0,\n                monthly_token_limit: 50000\n            };\n            const { error: profileError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from('user_profiles').insert(profileData);\n            if (profileError) {\n                console.error('⚠️ [REGISTER-FREE] Error creando perfil:', profileError);\n            // No fallar el registro por esto\n            } else {\n                console.log('✅ [REGISTER-FREE] Perfil creado exitosamente');\n            }\n        } catch (profileError) {\n            console.error('⚠️ [REGISTER-FREE] Error inesperado creando perfil:', profileError);\n        }\n        console.log('📧 [REGISTER-FREE] Usuario invitado exitosamente');\n        // Limpiar rate limiting en caso de éxito\n        registrationAttempts.delete(clientIP);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Te hemos enviado un enlace de invitación a tu email. Haz clic en el enlace para establecer tu contraseña y acceder a tu cuenta.',\n            flow: 'invitation'\n        });\n    } catch (error) {\n        console.error('Error en registro gratuito:', error);\n        // Actualizar rate limiting\n        registrationAttempts.set(clientIP, {\n            count: (registrationAttempts.get(clientIP)?.count || 0) + 1,\n            lastAttempt: Date.now()\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error interno del servidor. Por favor, intenta de nuevo.'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/register-free/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/admin.ts":
/*!***********************************!*\
  !*** ./src/lib/supabase/admin.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAdminService: () => (/* binding */ SupabaseAdminService),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n// src/lib/supabase/admin.ts\n// Cliente administrativo de Supabase para operaciones del servidor\n\n// Cliente admin con privilegios elevados\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Funciones de utilidad para operaciones administrativas\nclass SupabaseAdminService {\n    // Crear transacción de Stripe\n    static async createStripeTransaction(transaction) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').insert([\n            transaction\n        ]).select().single();\n        if (error) {\n            console.error('Error creating stripe transaction:', error);\n            throw new Error(`Failed to create transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener transacción por session ID\n    static async getTransactionBySessionId(sessionId) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').select('*').eq('stripe_session_id', sessionId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching transaction:', error);\n            throw new Error(`Failed to fetch transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear usuario con invitación\n    static async createUserWithInvitation(email, userData) {\n        console.log('🔄 [SUPABASE_ADMIN] Creating user invitation:', {\n            email,\n            userData,\n            redirectTo: `${\"http://localhost:3001\"}/auth/callback`,\n            timestamp: new Date().toISOString()\n        });\n        const { data, error } = await supabaseAdmin.auth.admin.inviteUserByEmail(email, {\n            data: userData,\n            redirectTo: `${\"http://localhost:3001\"}/auth/callback`\n        });\n        console.log('📊 [SUPABASE_ADMIN] Invitation result:', {\n            hasData: !!data,\n            hasUser: !!data?.user,\n            userId: data?.user?.id,\n            userEmail: data?.user?.email,\n            userAud: data?.user?.aud,\n            userRole: data?.user?.role,\n            emailConfirmed: data?.user?.email_confirmed_at,\n            userMetadata: data?.user?.user_metadata,\n            appMetadata: data?.user?.app_metadata,\n            error: error?.message,\n            errorCode: error?.status,\n            fullError: error\n        });\n        if (error) {\n            console.error('❌ [SUPABASE_ADMIN] Error creating user invitation:', {\n                message: error.message,\n                status: error.status,\n                details: error\n            });\n            throw new Error(`Failed to create user invitation: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear usuario con contraseña específica y opcionalmente enviar email de confirmación\n    static async createUserWithPassword(email, password, userData, sendConfirmationEmail = true) {\n        console.log('🔄 [SUPABASE_ADMIN] Creating user with password:', {\n            email,\n            userData,\n            sendConfirmationEmail,\n            timestamp: new Date().toISOString()\n        });\n        const { data, error } = await supabaseAdmin.auth.admin.createUser({\n            email,\n            password,\n            user_metadata: userData,\n            email_confirm: false // No confirmar automáticamente\n        });\n        console.log('📊 [SUPABASE_ADMIN] User creation result:', {\n            hasData: !!data,\n            hasUser: !!data?.user,\n            userId: data?.user?.id,\n            userEmail: data?.user?.email,\n            emailConfirmed: data?.user?.email_confirmed_at,\n            userMetadata: data?.user?.user_metadata,\n            error: error?.message,\n            errorCode: error?.status\n        });\n        if (error) {\n            console.error('❌ [SUPABASE_ADMIN] Error creating user with password:', {\n                message: error.message,\n                status: error.status,\n                details: error\n            });\n            return {\n                data: null,\n                error\n            };\n        }\n        // Enviar email de confirmación solo si se solicita\n        if (data?.user && sendConfirmationEmail) {\n            console.log('📧 Enviando email de confirmación...');\n            const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({\n                type: 'signup',\n                email: email,\n                password: password,\n                options: {\n                    redirectTo: `${\"http://localhost:3001\"}/auth/confirmed`\n                }\n            });\n            if (emailError) {\n                console.error('⚠️ Error enviando email de confirmación:', emailError);\n            // No fallar completamente, el usuario puede confirmar manualmente\n            } else {\n                console.log('✅ Email de confirmación enviado exitosamente');\n            }\n        } else if (data?.user && !sendConfirmationEmail) {\n            console.log('📧 Email de confirmación omitido (se enviará después del pago)');\n        }\n        return {\n            data,\n            error: null\n        };\n    }\n    // Enviar email de confirmación para usuario existente\n    static async sendConfirmationEmailForUser(userId) {\n        console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para usuario:', userId);\n        try {\n            // Obtener datos del usuario\n            const { data: userData, error: userError } = await supabaseAdmin.auth.admin.getUserById(userId);\n            if (userError || !userData?.user) {\n                console.error('Error obteniendo datos del usuario:', userError);\n                return {\n                    success: false,\n                    error: 'Usuario no encontrado'\n                };\n            }\n            const user = userData.user;\n            // Para usuarios pre-registrados, actualizar el estado de confirmación directamente\n            // ya que el pago exitoso confirma la intención del usuario\n            const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(user.id, {\n                email_confirm: true,\n                user_metadata: {\n                    ...user.user_metadata,\n                    payment_verified: true,\n                    email_confirmed_via_payment: true,\n                    confirmed_at: new Date().toISOString()\n                }\n            });\n            if (updateError) {\n                console.error('⚠️ Error confirmando email del usuario:', updateError);\n                return {\n                    success: false,\n                    error: updateError.message\n                };\n            }\n            console.log('✅ Usuario confirmado automáticamente después del pago exitoso');\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('Error en sendConfirmationEmailForUser:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Error desconocido'\n            };\n        }\n    }\n    // Enviar email de confirmación para usuario existente (método legacy)\n    static async sendConfirmationEmail(email, password) {\n        console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para:', email);\n        const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({\n            type: 'signup',\n            email: email,\n            password: password,\n            options: {\n                redirectTo: `${\"http://localhost:3001\"}/auth/confirmed`\n            }\n        });\n        if (emailError) {\n            console.error('⚠️ Error enviando email de confirmación:', emailError);\n            return {\n                success: false,\n                error: emailError.message\n            };\n        } else {\n            console.log('✅ Email de confirmación enviado exitosamente');\n            return {\n                success: true\n            };\n        }\n    }\n    // Crear perfil de usuario\n    static async createUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').insert([\n            profile\n        ]).select().single();\n        if (error) {\n            console.error('Error creating user profile:', error);\n            throw new Error(`Failed to create user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear o actualizar perfil de usuario\n    static async upsertUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').upsert([\n            profile\n        ], {\n            onConflict: 'user_id'\n        }).select().single();\n        if (error) {\n            console.error('Error upserting user profile:', error);\n            throw new Error(`Failed to upsert user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar cambio de plan\n    static async logPlanChange(planChange) {\n        const { data, error } = await supabaseAdmin.from('user_plan_history').insert([\n            planChange\n        ]).select().single();\n        if (error) {\n            console.error('Error logging plan change:', error);\n            throw new Error(`Failed to log plan change: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar acceso a característica\n    static async logFeatureAccess(accessLog) {\n        const { data, error } = await supabaseAdmin.from('feature_access_log').insert([\n            accessLog\n        ]).select().single();\n        if (error) {\n            console.error('Error logging feature access:', error);\n            throw new Error(`Failed to log feature access: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener perfil de usuario por ID\n    static async getUserProfile(userId) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').select('*').eq('user_id', userId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching user profile:', error);\n            throw new Error(`Failed to fetch user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Actualizar transacción con user_id\n    static async updateTransactionWithUser(transactionId, userId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            user_id: userId,\n            updated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error updating transaction with user_id:', error);\n            throw new Error(`Failed to update transaction: ${error.message}`);\n        }\n    }\n    // Activar transacción (marcar como activada)\n    static async activateTransaction(transactionId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            activated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error activating transaction:', error);\n            throw new Error(`Failed to activate transaction: ${error.message}`);\n        }\n    }\n    // Obtener conteo de documentos del usuario\n    static async getDocumentsCount(userId) {\n        const { count, error } = await supabaseAdmin.from('documentos').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', userId);\n        if (error) {\n            console.error('Error getting documents count:', error);\n            return 0; // Retornar 0 en caso de error en lugar de lanzar excepción\n        }\n        return count || 0;\n    }\n    // Obtener usuario por email desde Supabase Auth\n    static async getUserByEmail(email) {\n        try {\n            const { data: { users }, error } = await supabaseAdmin.auth.admin.listUsers();\n            if (error) {\n                console.error('Error getting user by email:', error);\n                throw new Error(`Failed to get user by email: ${error.message}`);\n            }\n            if (!users || users.length === 0) {\n                return null;\n            }\n            // Filtrar por email ya que la API no permite filtro directo\n            const user = users.find((u)=>u.email === email);\n            if (!user) {\n                return null;\n            }\n            return {\n                id: user.id,\n                email: user.email,\n                email_confirmed_at: user.email_confirmed_at\n            };\n        } catch (error) {\n            console.error('Error in getUserByEmail:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/admin.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister-free%2Froute&page=%2Fapi%2Fauth%2Fregister-free%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister-free%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();