// ===== Archivo: src/app/auth/reset-password/page.tsx (VERSIÓN FINAL Y MÁS ROBUSTA) =====
'use client';

import { useState, useEffect, Suspense, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import { toast } from 'react-hot-toast';
import { FiLock, <PERSON><PERSON><PERSON>ck, <PERSON><PERSON>oader, FiAlertTriangle } from 'react-icons/fi';

function ResetPasswordForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isVerifying, setIsVerifying] = useState(true);
  const [hasRecoverySession, setHasRecoverySession] = useState(false);
  const [linkError, setLinkError] = useState<string | null>(null);

  const verifySession = useCallback(async () => {
    const supabase = createClient();
    // Supabase maneja el token de la URL automáticamente.
    // El evento onAuthStateChange se encargará del resto.
    // Solo necesitamos darle tiempo para que procese.

    // Si después de un tiempo no hay sesión, es un error.
    setTimeout(async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session && isVerifying) {
        setLinkError('El enlace de recuperación es inválido o ha expirado. Por favor, solicita uno nuevo.');
        setIsVerifying(false);
      }
    }, 5000); // Aumentamos el timeout a 5s para darle más margen
  }, [isVerifying]);

  useEffect(() => {
    const supabase = createClient();
    
    // El listener es la forma más fiable de detectar el cambio de sesión.
    const { data: authListener } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === 'PASSWORD_RECOVERY' || (event === 'SIGNED_IN' && session)) {
        setHasRecoverySession(true);
        setIsVerifying(false);
      }
    });

    verifySession();

    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, [verifySession]);


  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!hasRecoverySession) {
      setError('No se ha establecido una sesión válida para cambiar la contraseña. Por favor, utiliza el enlace de tu email de nuevo.');
      toast.error('Error de sesión. Intenta usar el enlace de tu email de nuevo.');
      return;
    }

    if (password.length < 6) {
      setError('La nueva contraseña debe tener al menos 6 caracteres.');
      return;
    }

    if (password !== confirmPassword) {
      setError('Las contraseñas no coinciden.');
      return;
    }

    setLoading(true);
    const supabase = createClient();

    const { error: updateError } = await supabase.auth.updateUser({
      password,
    });
    setLoading(false);

    if (updateError) {
      setError(updateError.message === "Auth session missing!" ? "Error de sesión: Tu sesión ha expirado o es inválida. Por favor, usa el enlace de tu email de nuevo." : updateError.message);
      toast.error('Error al actualizar la contraseña.');
    } else {
      toast.success('¡Contraseña establecida exitosamente!');
      setTimeout(() => {
        router.push('/login');
      }, 1500);
    }
  };

  // ----- RENDERIZADO CONDICIONAL -----
  // (El JSX no cambia, es el mismo que ya tienes)
  if (isVerifying) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4">
        <FiLoader className="w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin" />
        <h2 className="text-xl font-semibold text-gray-800">Verificando enlace...</h2>
        <p className="text-gray-600 mt-2">Esto puede tardar unos segundos.</p>
      </div>
    );
  }

  if (linkError) {
    return (
      <div className="min-h-screen bg-red-50 flex flex-col justify-center items-center p-4">
        <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center">
          <FiAlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Enlace Inválido o Expirado</h2>
          <p className="text-gray-600 mb-6">{linkError}</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
          >
            Volver a Inicio de Sesión
          </button>
        </div>
      </div>
    );
  }

  if (!hasRecoverySession) {
     return (
       <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4">
        <FiLock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-700">Acceso no Autorizado</h2>
        <p className="text-gray-500 mt-2 mb-6 max-w-md text-center">
            Esta página es para establecer tu contraseña usando un enlace seguro. Si necesitas restablecer tu contraseña, solicítalo desde la página de inicio de sesión.
        </p>
         <button
            onClick={() => router.push('/login')}
            className="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
          >
            Ir a Inicio de Sesión
          </button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <FiLock className="w-12 h-12 text-blue-600" />
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Crea tu Nueva Contraseña
        </h2>
      </div>
      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">Nueva Contraseña</label>
              <div className="mt-1">
                <input id="password" name="password" type="password" required value={password} onChange={(e) => setPassword(e.target.value)} className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="Mínimo 6 caracteres"/>
              </div>
            </div>
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">Confirmar Nueva Contraseña</label>
              <div className="mt-1">
                <input id="confirmPassword" name="confirmPassword" type="password" required value={confirmPassword} onChange={(e) => setConfirmPassword(e.target.value)} className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="Repite la contraseña"/>
              </div>
            </div>
            {error && (
              <div className="text-red-500 text-sm bg-red-50 p-3 rounded-md border border-red-200">
                {error}
              </div>
            )}
            <div>
              <button type="submit" disabled={loading} className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
                {loading ? ( <><FiLoader className="animate-spin h-4 w-4 mr-2"/> Actualizando...</> ) : ( <><FiCheck className="h-4 w-4 mr-2"/> Establecer Contraseña</> )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default function ResetPasswordPage() {
  return (
    <Suspense fallback={<div>Cargando...</div>}>
      <ResetPasswordForm />
    </Suspense>
  );
}