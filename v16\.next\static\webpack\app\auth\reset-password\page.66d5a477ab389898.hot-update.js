"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/reset-password/page",{

/***/ "(app-pages-browser)/./src/app/auth/reset-password/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/auth/reset-password/page.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ResetPasswordPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiCheck_FiClock_FiLoader_FiLock_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FiCheck,FiClock,FiLoader,FiLock!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n// ===== Archivo: src\\app\\auth\\reset-password\\page.tsx =====\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Funciones auxiliares estables (fuera del componente)\nconst isRecoveryUrl = (addDebugInfo)=>{\n    const urlHash = window.location.hash;\n    const urlSearch = window.location.search;\n    // Múltiples métodos de detección\n    const hasRecoveryType = urlHash.includes('type=recovery') || urlSearch.includes('type=recovery');\n    const hasAccessToken = urlHash.includes('access_token=') || urlSearch.includes('access_token=');\n    const hasRefreshToken = urlHash.includes('refresh_token=') || urlSearch.includes('refresh_token=');\n    const hasCode = urlSearch.includes('code=');\n    const comesFromEmail = document.referrer === '' || document.referrer.includes('mail') || document.referrer.includes('gmail');\n    if (addDebugInfo) {\n        addDebugInfo(\"URL Analysis: hash=\".concat(urlHash.substring(0, 100), \", search=\").concat(urlSearch.substring(0, 100)));\n        addDebugInfo(\"Recovery indicators: type=\".concat(hasRecoveryType, \", access_token=\").concat(hasAccessToken, \", refresh_token=\").concat(hasRefreshToken, \", code=\").concat(hasCode, \", fromEmail=\").concat(comesFromEmail));\n    }\n    return hasRecoveryType || hasAccessToken && hasRefreshToken || hasCode;\n};\nconst checkUserMetadata = async (session, addDebugInfo)=>{\n    try {\n        if (!(session === null || session === void 0 ? void 0 : session.user)) return false;\n        const userMetadata = session.user.user_metadata || {};\n        const appMetadata = session.user.app_metadata || {};\n        if (addDebugInfo) {\n            addDebugInfo(\"User metadata: \".concat(JSON.stringify(userMetadata)));\n            addDebugInfo(\"App metadata: \".concat(JSON.stringify(appMetadata)));\n        }\n        // Verificar si hay indicadores de recovery en los metadatos\n        return userMetadata.recovery_flow === true || appMetadata.recovery_flow === true;\n    } catch (error) {\n        if (addDebugInfo) {\n            addDebugInfo(\"Error checking metadata: \".concat(error.message));\n        } else {\n            console.error(\"Error checking metadata: \".concat(error.message));\n        }\n        return false;\n    }\n};\nfunction ResetPasswordForm() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)(); // Necesario para leer 'error' y 'error_description'\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [confirmPassword, setConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Estados mejorados para mejor control del flujo\n    const [isVerifyingToken, setIsVerifyingToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [hasRecoverySession, setHasRecoverySession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [linkError, setLinkError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [debugInfo, setDebugInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Función helper para logging (estable)\n    const addDebugInfo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ResetPasswordForm.useCallback[addDebugInfo]\": (message)=>{\n            const timestamp = new Date().toISOString();\n            const logMessage = \"[\".concat(timestamp, \"] \").concat(message);\n            console.log(logMessage);\n            setDebugInfo({\n                \"ResetPasswordForm.useCallback[addDebugInfo]\": (prev)=>[\n                        ...prev,\n                        logMessage\n                    ]\n            }[\"ResetPasswordForm.useCallback[addDebugInfo]\"]);\n        }\n    }[\"ResetPasswordForm.useCallback[addDebugInfo]\"], []);\n    // useEffect principal para la lógica de autenticación\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ResetPasswordForm.useEffect\": ()=>{\n            addDebugInfo(\"ResetPasswordForm: useEffect AUTH iniciado. URL: \" + window.location.href);\n            setIsVerifyingToken(true);\n            setHasRecoverySession(false);\n            setLinkError(null);\n            // Verificar errores explícitos en los query parameters (enviados por Supabase si el token/code es inválido)\n            const urlErrorParam = searchParams.get('error');\n            const errorCodeParam = searchParams.get('error_code');\n            const errorDescriptionParam = searchParams.get('error_description');\n            if (urlErrorParam || errorCodeParam || errorDescriptionParam) {\n                let userFriendlyMessage = decodeURIComponent(errorDescriptionParam || urlErrorParam || 'Error desconocido en el enlace.');\n                if (userFriendlyMessage.toLowerCase().includes('link is invalid or has expired') || userFriendlyMessage.toLowerCase().includes('token has expired') || errorCodeParam === 'token_expired_or_invalid') {\n                    userFriendlyMessage = 'El enlace de recuperación ha expirado o ya fue utilizado. Por favor, solicita un nuevo enlace.';\n                }\n                addDebugInfo(\"Error explícito en URL: \" + JSON.stringify({\n                    urlErrorParam,\n                    errorCodeParam,\n                    errorDescriptionParam\n                }));\n                setLinkError(userFriendlyMessage);\n                setHasRecoverySession(false);\n                setIsVerifyingToken(false);\n                return;\n            }\n            const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.createClient)();\n            // Verificación inicial de sesión existente mejorada\n            const checkInitialSession = {\n                \"ResetPasswordForm.useEffect.checkInitialSession\": async ()=>{\n                    try {\n                        addDebugInfo(\"Verificando sesión inicial...\");\n                        // Primero verificar si la URL indica recovery\n                        const urlIndicatesRecovery = isRecoveryUrl(addDebugInfo);\n                        addDebugInfo(\"URL indica recovery: \".concat(urlIndicatesRecovery));\n                        const { data: { session: currentSession }, error } = await supabase.auth.getSession();\n                        if (error) {\n                            addDebugInfo(\"Error obteniendo sesión inicial: \" + error.message);\n                            // Si hay error pero la URL indica recovery, esperar a los eventos\n                            if (urlIndicatesRecovery) {\n                                addDebugInfo(\"URL indica recovery, esperando eventos de auth...\");\n                                return false; // No establecer error aún\n                            }\n                            return false;\n                        }\n                        if (currentSession) {\n                            var _currentSession_user;\n                            addDebugInfo(\"Sesión inicial encontrada. User ID: \" + ((_currentSession_user = currentSession.user) === null || _currentSession_user === void 0 ? void 0 : _currentSession_user.id));\n                            // Múltiples verificaciones para detectar recovery\n                            const urlBasedRecovery = urlIndicatesRecovery;\n                            const metadataBasedRecovery = await checkUserMetadata(currentSession, addDebugInfo);\n                            if (urlBasedRecovery || metadataBasedRecovery) {\n                                addDebugInfo(\"Recovery detectado - URL: \".concat(urlBasedRecovery, \", Metadata: \").concat(metadataBasedRecovery));\n                                setHasRecoverySession(true);\n                                setIsVerifyingToken(false);\n                                return true;\n                            } else {\n                                addDebugInfo(\"Sesión encontrada pero no es de recovery\");\n                            }\n                        } else if (urlIndicatesRecovery) {\n                            addDebugInfo(\"No hay sesión pero URL indica recovery - esperando procesamiento...\");\n                            // No establecer error, esperar a que Supabase procese la URL\n                            return false;\n                        }\n                        return false;\n                    } catch (error) {\n                        addDebugInfo(\"Error en verificación inicial: \" + error.message);\n                        return false;\n                    }\n                }\n            }[\"ResetPasswordForm.useEffect.checkInitialSession\"];\n            // Configurar listener de eventos de autenticación\n            const { data: authListener } = supabase.auth.onAuthStateChange({\n                \"ResetPasswordForm.useEffect\": async (event, session)=>{\n                    var _session_user;\n                    addDebugInfo(\"Evento onAuthStateChange: \".concat(event, \", Session: \").concat(!!session, \", User: \").concat(session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id));\n                    if (event === 'PASSWORD_RECOVERY') {\n                        if (session) {\n                            addDebugInfo(\"Sesión establecida por evento PASSWORD_RECOVERY\");\n                            setHasRecoverySession(true);\n                            setLinkError(null);\n                            setIsVerifyingToken(false);\n                        } else {\n                            addDebugInfo(\"Evento PASSWORD_RECOVERY sin sesión - token inválido o expirado\");\n                            setLinkError('El enlace de recuperación parece ser inválido o ha expirado.');\n                            setHasRecoverySession(false);\n                            setIsVerifyingToken(false);\n                        }\n                    } else if (event === 'INITIAL_SESSION') {\n                        if (session) {\n                            addDebugInfo(\"Evento INITIAL_SESSION con sesión - verificando si es recovery\");\n                            // Usar métodos mejorados de detección\n                            const urlBasedRecovery = isRecoveryUrl(addDebugInfo);\n                            const metadataBasedRecovery = await checkUserMetadata(session, addDebugInfo);\n                            if (urlBasedRecovery || metadataBasedRecovery) {\n                                addDebugInfo(\"Recovery confirmado en INITIAL_SESSION - URL: \".concat(urlBasedRecovery, \", Metadata: \").concat(metadataBasedRecovery));\n                                setHasRecoverySession(true);\n                                setIsVerifyingToken(false);\n                            } else {\n                                addDebugInfo(\"Sesión normal en INITIAL_SESSION - no es recovery\");\n                                setIsVerifyingToken(false);\n                            }\n                        } else {\n                            addDebugInfo(\"Evento INITIAL_SESSION sin sesión\");\n                            // Si no hay sesión pero la URL indica recovery, seguir esperando\n                            if (isRecoveryUrl(addDebugInfo)) {\n                                addDebugInfo(\"Sin sesión pero URL indica recovery - continuando verificación...\");\n                            } else {\n                                setIsVerifyingToken(false);\n                            }\n                        }\n                    } else if (event === 'SIGNED_IN') {\n                        addDebugInfo(\"Evento SIGNED_IN\");\n                        // Verificar si es una sesión de recovery\n                        if (session) {\n                            const urlBasedRecovery = isRecoveryUrl(addDebugInfo);\n                            const metadataBasedRecovery = await checkUserMetadata(session, addDebugInfo);\n                            if (urlBasedRecovery || metadataBasedRecovery) {\n                                addDebugInfo(\"Recovery detectado en SIGNED_IN - URL: \".concat(urlBasedRecovery, \", Metadata: \").concat(metadataBasedRecovery));\n                                setHasRecoverySession(true);\n                            }\n                        }\n                        setIsVerifyingToken(false);\n                    } else if (event === 'SIGNED_OUT') {\n                        addDebugInfo(\"Evento SIGNED_OUT\");\n                        setHasRecoverySession(false);\n                        setIsVerifyingToken(false);\n                    }\n                }\n            }[\"ResetPasswordForm.useEffect\"]);\n            // Ejecutar verificación inicial\n            checkInitialSession();\n            return ({\n                \"ResetPasswordForm.useEffect\": ()=>{\n                    authListener === null || authListener === void 0 ? void 0 : authListener.subscription.unsubscribe();\n                    addDebugInfo(\"Listener de autenticación limpiado\");\n                }\n            })[\"ResetPasswordForm.useEffect\"];\n        }\n    }[\"ResetPasswordForm.useEffect\"], [\n        searchParams,\n        addDebugInfo\n    ]); // Dependencias corregidas\n    // useEffect separado para el timeout de seguridad\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ResetPasswordForm.useEffect\": ()=>{\n            // Solo ejecutar si estamos verificando y no hay un error de enlace ya detectado\n            if (!isVerifyingToken || linkError) {\n                return;\n            }\n            addDebugInfo(\"ResetPasswordForm: useEffect TIMEOUT iniciado.\");\n            const timeoutId = setTimeout({\n                \"ResetPasswordForm.useEffect.timeoutId\": async ()=>{\n                    // Volver a verificar el estado actual en el momento del timeout\n                    if (isVerifyingToken && !hasRecoverySession) {\n                        const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.createClient)();\n                        const { data: { session: finalCheckSession } } = await supabase.auth.getSession();\n                        if (!(finalCheckSession === null || finalCheckSession === void 0 ? void 0 : finalCheckSession.user) || !await checkUserMetadata(finalCheckSession, addDebugInfo) && !isRecoveryUrl(addDebugInfo)) {\n                            addDebugInfo(\"❌ TIMEOUT (5 min): No se estableció sesión de recuperación válida.\");\n                            setLinkError(\"No se pudo establecer una sesión para cambiar la contraseña. El enlace podría ser inválido o haber expirado.\");\n                            setHasRecoverySession(false);\n                        }\n                        // En cualquier caso, después del timeout, ya no estamos \"verificando\" de esta manera.\n                        setIsVerifyingToken(false);\n                    }\n                }\n            }[\"ResetPasswordForm.useEffect.timeoutId\"], 300000); // 5 minutos\n            return ({\n                \"ResetPasswordForm.useEffect\": ()=>{\n                    clearTimeout(timeoutId);\n                    addDebugInfo(\"Timeout de seguridad limpiado\");\n                }\n            })[\"ResetPasswordForm.useEffect\"];\n        }\n    }[\"ResetPasswordForm.useEffect\"], [\n        isVerifyingToken,\n        hasRecoverySession,\n        linkError,\n        addDebugInfo\n    ]);\n    // ... (handleSubmit y JSX igual que en la versión anterior)\n    const handleSubmit = async (e)=>{\n        var _userObjectBeforeUpdate_user_user_metadata, _userObjectBeforeUpdate_user_user_metadata1;\n        e.preventDefault();\n        setError('');\n        if (!hasRecoverySession) {\n            setError('No se ha establecido una sesión válida para cambiar la contraseña. Por favor, asegúrate de usar el enlace de tu email.');\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Error de sesión. Intenta usar el enlace de tu email de nuevo.');\n            return;\n        }\n        if (password.length < 6) {\n            setError('La nueva contraseña debe tener al menos 6 caracteres.');\n            return;\n        }\n        if (password !== confirmPassword) {\n            setError('Las contraseñas no coinciden.');\n            return;\n        }\n        setLoading(true);\n        const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.createClient)();\n        const { data: userObjectBeforeUpdate, error: getUserError } = await supabase.auth.getUser();\n        if (getUserError || !userObjectBeforeUpdate.user) {\n            setError('No se pudo verificar la sesión actual antes de actualizar. Intenta de nuevo.');\n            setLoading(false);\n            return;\n        }\n        const isInitialSetup = ((_userObjectBeforeUpdate_user_user_metadata = userObjectBeforeUpdate.user.user_metadata) === null || _userObjectBeforeUpdate_user_user_metadata === void 0 ? void 0 : _userObjectBeforeUpdate_user_user_metadata.requires_terms_acceptance_and_final_password_setup) === true || ((_userObjectBeforeUpdate_user_user_metadata1 = userObjectBeforeUpdate.user.user_metadata) === null || _userObjectBeforeUpdate_user_user_metadata1 === void 0 ? void 0 : _userObjectBeforeUpdate_user_user_metadata1.requires_initial_password_change) === true;\n        const metadataToUpdate = {};\n        if (isInitialSetup) {\n            metadataToUpdate.requires_terms_acceptance_and_final_password_setup = false;\n            metadataToUpdate.temporary_password_set = false;\n            metadataToUpdate.requires_initial_password_change = false;\n        }\n        const { error: updateError } = await supabase.auth.updateUser({\n            password,\n            data: metadataToUpdate\n        });\n        setLoading(false);\n        if (updateError) {\n            console.error(\"ResetPasswordForm: Error al actualizar contraseña:\", updateError);\n            setError(updateError.message === \"Auth session missing!\" ? \"Error de sesión: Tu sesión ha expirado o es inválida. Por favor, usa el enlace de tu email de nuevo.\" : updateError.message);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error(updateError.message === \"Auth session missing!\" ? \"Error de sesión. Usa el enlace de tu email.\" : 'Error al actualizar la contraseña.');\n        } else {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.success('¡Contraseña actualizada exitosamente!');\n            addDebugInfo(\"Contraseña actualizada exitosamente. Redirigiendo a login...\");\n            // Pequeña pausa para que el usuario vea el mensaje de éxito\n            setTimeout(()=>{\n                router.push('/login');\n            }, 1500);\n        }\n    };\n    // Renderizado condicional mejorado\n    if (isVerifyingToken) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCheck_FiClock_FiLoader_FiLock_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiLoader, {\n                    className: \"w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-gray-800\",\n                    children: \"Verificando enlace...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 319,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mt-2\",\n                    children: \"Esto puede tardar unos segundos.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 9\n                }, this),\n                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                    className: \"mt-4 text-xs text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                            children: \"Debug Info\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"mt-2 p-2 bg-gray-100 rounded text-left max-w-md overflow-auto\",\n                            children: debugInfo.join('\\n')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n            lineNumber: 317,\n            columnNumber: 7\n        }, this);\n    }\n    if (linkError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-red-50 flex flex-col justify-center items-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCheck_FiClock_FiLoader_FiLock_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiClock, {\n                        className: \"w-12 h-12 text-red-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-800 mb-2\",\n                        children: \"Enlace Inv\\xe1lido o Expirado\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: linkError\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/login'),\n                        className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors\",\n                        children: \"Volver a Inicio de Sesi\\xf3n\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                lineNumber: 336,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n            lineNumber: 335,\n            columnNumber: 7\n        }, this);\n    }\n    if (!hasRecoverySession && !isVerifyingToken) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCheck_FiClock_FiLoader_FiLock_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiLock, {\n                    className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 354,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-gray-700\",\n                    children: \"Acceso no Autorizado\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 355,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500 mt-2 mb-6 max-w-md text-center\",\n                    children: \"Esta p\\xe1gina es para establecer o restablecer tu contrase\\xf1a usando un enlace seguro enviado a tu email. Si necesitas restablecer tu contrase\\xf1a, solic\\xedtalo desde la p\\xe1gina de inicio de sesi\\xf3n.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 356,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>router.push('/login'),\n                    className: \"bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors\",\n                    children: \"Ir a Inicio de Sesi\\xf3n\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 360,\n                    columnNumber: 10\n                }, this),\n                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                    className: \"mt-4 text-xs text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                            children: \"Debug Info\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"mt-2 p-2 bg-gray-100 rounded text-left max-w-md overflow-auto\",\n                            children: debugInfo.join('\\n')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 367,\n                    columnNumber: 13\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n            lineNumber: 353,\n            columnNumber: 8\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCheck_FiClock_FiLoader_FiLock_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiLock, {\n                            className: \"w-12 h-12 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n                        children: \"Crea tu Nueva Contrase\\xf1a\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                lineNumber: 381,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        className: \"space-y-6\",\n                        onSubmit: handleSubmit,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"password\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Nueva Contrase\\xf1a\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"password\",\n                                            name: \"password\",\n                                            type: \"password\",\n                                            required: true,\n                                            value: password,\n                                            onChange: (e)=>setPassword(e.target.value),\n                                            className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            placeholder: \"M\\xednimo 6 caracteres\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"confirmPassword\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Confirmar Nueva Contrase\\xf1a\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"confirmPassword\",\n                                            name: \"confirmPassword\",\n                                            type: \"password\",\n                                            required: true,\n                                            value: confirmPassword,\n                                            onChange: (e)=>setConfirmPassword(e.target.value),\n                                            className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            placeholder: \"Repite la contrase\\xf1a\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-red-500 text-sm bg-red-50 p-3 rounded-md border border-red-200\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading || !hasRecoverySession,\n                                    className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCheck_FiClock_FiLoader_FiLock_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiLoader, {\n                                                className: \"animate-spin h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 32\n                                            }, this),\n                                            \" Actualizando...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCheck_FiClock_FiLoader_FiLock_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiCheck, {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 109\n                                            }, this),\n                                            \" Establecer Contrase\\xf1a\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 391,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                lineNumber: 390,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n        lineNumber: 379,\n        columnNumber: 5\n    }, this);\n} // Cierre de ResetPasswordForm\n_s(ResetPasswordForm, \"uwbHsRhrDzur5FKiOb7GKXlVpxA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ResetPasswordForm;\nfunction ResetPasswordPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex flex-col justify-center items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCheck_FiClock_FiLoader_FiLock_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiLoader, {\n                    className: \"w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 428,\n                    columnNumber: 13\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-gray-800\",\n                    children: \"Cargando...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 429,\n                    columnNumber: 13\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n            lineNumber: 427,\n            columnNumber: 9\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResetPasswordForm, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n            lineNumber: 432,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n        lineNumber: 426,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ResetPasswordPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ResetPasswordForm\");\n$RefreshReg$(_c1, \"ResetPasswordPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/reset-password/page.tsx\n"));

/***/ })

});