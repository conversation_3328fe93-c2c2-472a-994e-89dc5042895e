// ===== Archivo: src\app\payment\page.tsx =====
'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { getPlanById } from '@/lib/stripe/plans';
import toast from 'react-hot-toast';

function PaymentContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const planId = searchParams.get('plan') || 'free';
  
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [customerName, setCustomerName] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const plan = getPlanById(planId);

  useEffect(() => {
    if (!plan) {
      router.push('/'); // Redirigir si el plan no es válido
    }
  }, [plan, router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validaciones básicas
    if (!email.trim()) {
      toast.error('Por favor, ingresa tu email');
      return;
    }

    // Solo validar contraseña para planes de pago
    if (planId !== 'free') {
      if (!password.trim()) {
        toast.error('Por favor, ingresa una contraseña');
        return;
      }

      if (password.length < 6) {
        toast.error('La contraseña debe tener al menos 6 caracteres');
        return;
      }

      if (password !== confirmPassword) {
        toast.error('Las contraseñas no coinciden');
        return;
      }
    }

    setIsLoading(true);

    try {
      // Para el plan gratuito
      if (planId === 'free') {
        // Llamar al endpoint de registro gratuito (sin contraseña)
        const registerResponse = await fetch('/api/auth/register-free', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email,
            customerName,
          }),
        });

        const registerData = await registerResponse.json();

        if (registerResponse.ok && registerData.success) {
          toast.success('¡Casi listo! Revisa tu email para establecer tu contraseña y activar tu cuenta.');
          router.push(`/thank-you?plan=${planId}&email_sent=true`);
        } else {
          // Manejo de errores del endpoint de registro
          if (registerResponse.status === 429) {
            toast.error('Demasiados intentos. Inténtalo en 15 minutos.');
          } else {
            toast.error(registerData.error || 'Error al crear la cuenta gratuita');
          }
        }
      } else {
        // NUEVO FLUJO: Para planes de pago, crear usuario primero y luego ir a Stripe
        console.log('🔄 Iniciando nuevo flujo de pre-registro para plan de pago');

        // Paso 1: Pre-registrar usuario
        const preRegisterResponse = await fetch('/api/auth/pre-register-paid', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email,
            password,
            customerName: customerName || email.split('@')[0],
            planId
          }),
        });

        const preRegisterData = await preRegisterResponse.json();

        if (!preRegisterResponse.ok) {
          toast.error(preRegisterData.error || 'Error al crear la cuenta');
          return;
        }

        console.log('✅ Usuario pre-registrado exitosamente:', preRegisterData.userId);

        // Paso 2: Crear sesión de Stripe con el userId
        const stripeResponse = await fetch('/api/stripe/create-checkout-session', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            planId,
            email,
            customerName,
            userId: preRegisterData.userId, // Incluir el userId del usuario pre-registrado
          }),
        });

        const stripeData = await stripeResponse.json();

        if (stripeResponse.ok && stripeData.url) {
          console.log('🔄 Redirigiendo a Stripe Checkout...');
          window.location.href = stripeData.url; // Redirigir a Stripe Checkout
        } else {
          toast.error(stripeData.error || 'Error al crear la sesión de pago');
        }
      }
    } catch (error) {
      console.error('Error en handleSubmit:', error);
      toast.error('Error al procesar la solicitud. Por favor, intenta de nuevo.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!plan) {
    // Este return se activará si el useEffect redirige, o si el plan es inválido inicialmente
    return (
        <div className="min-h-screen flex items-center justify-center">
            Cargando detalles del plan o redirigiendo...
        </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            {plan.name}
          </h1>
          <p className="text-2xl font-semibold text-blue-600 mt-2">
            {plan.price === 0 ? 'Gratis' : `€${(plan.price / 100).toFixed(2)}`}
            {(planId === 'pro' || planId === 'usuario') && plan.price > 0 && <span className="text-sm text-gray-500">/mes</span>}
          </p>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">
              Características del plan:
            </h3>
            <ul className="space-y-2">
              {plan.features.map((feature, index) => {
                // Si es un encabezado (Incluye: o No incluye:) - SIN ICONO
                if (feature === 'Incluye:' || feature === 'No incluye:') {
                  return (
                    <li key={index} className="mt-4 first:mt-0">
                      <span className="text-sm font-semibold text-gray-900">{feature}</span>
                    </li>
                  );
                }

                // Lógica mejorada para determinar si un ítem está bajo "No incluye:"
                const isNotIncludedItem = (() => {
                  if (!feature.startsWith('• ')) return false;

                  // Buscar hacia atrás el encabezado más cercano
                  for (let i = index - 1; i >= 0; i--) {
                    if (plan.features[i] === 'Incluye:') return false;
                    if (plan.features[i] === 'No incluye:') return true;
                  }
                  return false;
                })();

                return (
                  <li key={index} className="flex items-start ml-2">
                    {feature.startsWith('• ') ? (
                      isNotIncludedItem ? (
                        // Icono de Cruz Roja para características no incluidas
                        <svg
                          className="h-4 w-4 mt-0.5 mr-3 flex-shrink-0 text-red-500"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </svg>
                      ) : (
                        // Icono de Check Verde para características incluidas
                        <svg
                          className="h-4 w-4 mt-0.5 mr-3 flex-shrink-0 text-green-500"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      )
                    ) : null}
                    <span className="text-sm text-gray-600">
                      {feature.startsWith('• ') ? feature.substring(2) : feature}
                    </span>
                  </li>
                );
              })}
            </ul>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email *
              </label>
              <input
                type="email"
                id="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="<EMAIL>"
                disabled={isLoading}
              />
            </div>

            {/* Solo mostrar campos de contraseña para planes de pago */}
            {planId !== 'free' && (
              <>
                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                    Contraseña *
                  </label>
                  <input
                    type="password"
                    id="password"
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Mínimo 6 caracteres"
                    disabled={isLoading}
                    minLength={6}
                  />
                </div>

                <div>
                  <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                    Confirmar Contraseña *
                  </label>
                  <input
                    type="password"
                    id="confirmPassword"
                    required
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Repite tu contraseña"
                    disabled={isLoading}
                    minLength={6}
                  />
                </div>
              </>
            )}

            {/* Mostrar mensaje informativo para plan gratuito */}
            {planId === 'free' && (
              <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                <p className="text-sm text-blue-700">
                  📧 Te enviaremos un email para que establezcas tu contraseña y actives tu cuenta.
                </p>
              </div>
            )}

            <div>
              <label htmlFor="customerName" className="block text-sm font-medium text-gray-700">
                Nombre (opcional)
              </label>
              <input
                type="text"
                id="customerName"
                value={customerName}
                onChange={(e) => setCustomerName(e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Tu nombre"
                disabled={isLoading}
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Procesando...' : 
               planId === 'free' ? 'Solicitar Acceso Gratuito' : 'Proceder al Pago'}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}

export default function PaymentPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md mx-auto">
          <div className="bg-white shadow rounded-lg p-6">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-4 text-gray-600">Cargando...</p>
            </div>
          </div>
        </div>
      </div>
    }>
      <PaymentContent />
    </Suspense>
  );
}