/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/payment/page"],{

/***/ "(app-pages-browser)/./node_modules/goober/dist/goober.modern.js":
/*!***************************************************!*\
  !*** ./node_modules/goober/dist/goober.modern.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   css: () => (/* binding */ u),\n/* harmony export */   extractCss: () => (/* binding */ r),\n/* harmony export */   glob: () => (/* binding */ b),\n/* harmony export */   keyframes: () => (/* binding */ h),\n/* harmony export */   setup: () => (/* binding */ m),\n/* harmony export */   styled: () => (/* binding */ j)\n/* harmony export */ });\nlet e={data:\"\"},t=t=>\"object\"==typeof window?((t?t.querySelector(\"#_goober\"):window._goober)||Object.assign((t||document.head).appendChild(document.createElement(\"style\")),{innerHTML:\" \",id:\"_goober\"})).firstChild:t||e,r=e=>{let r=t(e),l=r.data;return r.data=\"\",l},l=/(?:([\\u0080-\\uFFFF\\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\\s*)/g,a=/\\/\\*[^]*?\\*\\/|  +/g,n=/\\n+/g,o=(e,t)=>{let r=\"\",l=\"\",a=\"\";for(let n in e){let c=e[n];\"@\"==n[0]?\"i\"==n[1]?r=n+\" \"+c+\";\":l+=\"f\"==n[1]?o(c,n):n+\"{\"+o(c,\"k\"==n[1]?\"\":t)+\"}\":\"object\"==typeof c?l+=o(c,t?t.replace(/([^,])+/g,e=>n.replace(/([^,]*:\\S+\\([^)]*\\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+\" \"+t:t)):n):null!=c&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,\"-$&\").toLowerCase(),a+=o.p?o.p(n,c):n+\":\"+c+\";\")}return r+(t&&a?t+\"{\"+a+\"}\":a)+l},c={},s=e=>{if(\"object\"==typeof e){let t=\"\";for(let r in e)t+=r+s(e[r]);return t}return e},i=(e,t,r,i,p)=>{let u=s(e),d=c[u]||(c[u]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return\"go\"+r})(u));if(!c[d]){let t=u!==e?e:(e=>{let t,r,o=[{}];for(;t=l.exec(e.replace(a,\"\"));)t[4]?o.shift():t[3]?(r=t[3].replace(n,\" \").trim(),o.unshift(o[0][r]=o[0][r]||{})):o[0][t[1]]=t[2].replace(n,\" \").trim();return o[0]})(e);c[d]=o(p?{[\"@keyframes \"+d]:t}:t,r?\"\":\".\"+d)}let f=r&&c.g?c.g:null;return r&&(c.g=c[d]),((e,t,r,l)=>{l?t.data=t.data.replace(l,e):-1===t.data.indexOf(e)&&(t.data=r?e+t.data:t.data+e)})(c[d],t,i,f),d},p=(e,t,r)=>e.reduce((e,l,a)=>{let n=t[a];if(n&&n.call){let e=n(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;n=t?\".\"+t:e&&\"object\"==typeof e?e.props?\"\":o(e,\"\"):!1===e?\"\":e}return e+l+(null==n?\"\":n)},\"\");function u(e){let r=this||{},l=e.call?e(r.p):e;return i(l.unshift?l.raw?p(l,[].slice.call(arguments,1),r.p):l.reduce((e,t)=>Object.assign(e,t&&t.call?t(r.p):t),{}):l,t(r.target),r.g,r.o,r.k)}let d,f,g,b=u.bind({g:1}),h=u.bind({k:1});function m(e,t,r,l){o.p=t,d=e,f=r,g=l}function j(e,t){let r=this||{};return function(){let l=arguments;function a(n,o){let c=Object.assign({},n),s=c.className||a.className;r.p=Object.assign({theme:f&&f()},c),r.o=/ *go\\d+/.test(s),c.className=u.apply(r,l)+(s?\" \"+s:\"\"),t&&(c.ref=o);let i=e;return e[0]&&(i=c.as||e,delete c.as),g&&i[0]&&g(c),d(i,c)}return t?t(a):a}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/goober/dist/goober.modern.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcdjE2XFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGFwaVxcbmF2aWdhdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuLi9jbGllbnQvY29tcG9uZW50cy9uYXZpZ2F0aW9uJztcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bmF2aWdhdGlvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cpayment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cpayment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/payment/page.tsx */ \"(app-pages-browser)/./src/app/payment/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbmFhdGElNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDT3Bvc0klNUMlNUN2MTYlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYXltZW50JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSw4S0FBMkgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG5hYXRhXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXE9wb3NJXFxcXHYxNlxcXFxzcmNcXFxcYXBwXFxcXHBheW1lbnRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cpayment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXHYxNlxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxccmVhY3RcXGpzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_tagged_template_literal.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next/node_modules/@swc/helpers/esm/_tagged_template_literal.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _: () => (/* binding */ _tagged_template_literal)\n/* harmony export */ });\nfunction _tagged_template_literal(strings, raw) {\n    if (!raw) raw = strings.slice(0);\n\n    return Object.freeze(Object.defineProperties(strings, { raw: { value: Object.freeze(raw) } }));\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L25vZGVfbW9kdWxlcy9Ac3djL2hlbHBlcnMvZXNtL190YWdnZWRfdGVtcGxhdGVfbGl0ZXJhbC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTs7QUFFQSw0REFBNEQsT0FBTyw2QkFBNkI7QUFDaEc7QUFDeUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXHYxNlxcbm9kZV9tb2R1bGVzXFxuZXh0XFxub2RlX21vZHVsZXNcXEBzd2NcXGhlbHBlcnNcXGVzbVxcX3RhZ2dlZF90ZW1wbGF0ZV9saXRlcmFsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIF90YWdnZWRfdGVtcGxhdGVfbGl0ZXJhbChzdHJpbmdzLCByYXcpIHtcbiAgICBpZiAoIXJhdykgcmF3ID0gc3RyaW5ncy5zbGljZSgwKTtcblxuICAgIHJldHVybiBPYmplY3QuZnJlZXplKE9iamVjdC5kZWZpbmVQcm9wZXJ0aWVzKHN0cmluZ3MsIHsgcmF3OiB7IHZhbHVlOiBPYmplY3QuZnJlZXplKHJhdykgfSB9KSk7XG59XG5leHBvcnQgeyBfdGFnZ2VkX3RlbXBsYXRlX2xpdGVyYWwgYXMgXyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_tagged_template_literal.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/react-hot-toast/dist/index.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CheckmarkIcon: () => (/* binding */ _),\n/* harmony export */   ErrorIcon: () => (/* binding */ k),\n/* harmony export */   LoaderIcon: () => (/* binding */ V),\n/* harmony export */   ToastBar: () => (/* binding */ C),\n/* harmony export */   ToastIcon: () => (/* binding */ M),\n/* harmony export */   Toaster: () => (/* binding */ Oe),\n/* harmony export */   \"default\": () => (/* binding */ Vt),\n/* harmony export */   resolveValue: () => (/* binding */ f),\n/* harmony export */   toast: () => (/* binding */ c),\n/* harmony export */   useToaster: () => (/* binding */ O),\n/* harmony export */   useToasterStore: () => (/* binding */ D)\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var goober__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! goober */ \"(app-pages-browser)/./node_modules/goober/dist/goober.modern.js\");\n/* __next_internal_client_entry_do_not_use__ CheckmarkIcon,ErrorIcon,LoaderIcon,ToastBar,ToastIcon,Toaster,default,resolveValue,toast,useToaster,useToasterStore auto */ \nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0) rotate(45deg);\\n\topacity: 0;\\n}\\nto {\\n transform: scale(1) rotate(45deg);\\n  opacity: 1;\\n}\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0);\\n  opacity: 0;\\n}\\nto {\\n  transform: scale(1);\\n  opacity: 1;\\n}\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0) rotate(90deg);\\n\topacity: 0;\\n}\\nto {\\n  transform: scale(1) rotate(90deg);\\n\topacity: 1;\\n}\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject3() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  width: 20px;\\n  opacity: 0;\\n  height: 20px;\\n  border-radius: 10px;\\n  background: \",\n        \";\\n  position: relative;\\n  transform: rotate(45deg);\\n\\n  animation: \",\n        \" 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\\n    forwards;\\n  animation-delay: 100ms;\\n\\n  &:after,\\n  &:before {\\n    content: '';\\n    animation: \",\n        \" 0.15s ease-out forwards;\\n    animation-delay: 150ms;\\n    position: absolute;\\n    border-radius: 3px;\\n    opacity: 0;\\n    background: \",\n        \";\\n    bottom: 9px;\\n    left: 4px;\\n    height: 2px;\\n    width: 12px;\\n  }\\n\\n  &:before {\\n    animation: \",\n        \" 0.15s ease-out forwards;\\n    animation-delay: 180ms;\\n    transform: rotate(90deg);\\n  }\\n\"\n    ]);\n    _templateObject3 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject4() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n\"\n    ]);\n    _templateObject4 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject5() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  width: 12px;\\n  height: 12px;\\n  box-sizing: border-box;\\n  border: 2px solid;\\n  border-radius: 100%;\\n  border-color: \",\n        \";\\n  border-right-color: \",\n        \";\\n  animation: \",\n        \" 1s linear infinite;\\n\"\n    ]);\n    _templateObject5 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject6() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0) rotate(45deg);\\n\topacity: 0;\\n}\\nto {\\n  transform: scale(1) rotate(45deg);\\n\topacity: 1;\\n}\"\n    ]);\n    _templateObject6 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject7() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n0% {\\n\theight: 0;\\n\twidth: 0;\\n\topacity: 0;\\n}\\n40% {\\n  height: 0;\\n\twidth: 6px;\\n\topacity: 1;\\n}\\n100% {\\n  opacity: 1;\\n  height: 10px;\\n}\"\n    ]);\n    _templateObject7 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject8() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  width: 20px;\\n  opacity: 0;\\n  height: 20px;\\n  border-radius: 10px;\\n  background: \",\n        \";\\n  position: relative;\\n  transform: rotate(45deg);\\n\\n  animation: \",\n        \" 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\\n    forwards;\\n  animation-delay: 100ms;\\n  &:after {\\n    content: '';\\n    box-sizing: border-box;\\n    animation: \",\n        \" 0.2s ease-out forwards;\\n    opacity: 0;\\n    animation-delay: 200ms;\\n    position: absolute;\\n    border-right: 2px solid;\\n    border-bottom: 2px solid;\\n    border-color: \",\n        \";\\n    bottom: 6px;\\n    left: 6px;\\n    height: 10px;\\n    width: 6px;\\n  }\\n\"\n    ]);\n    _templateObject8 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject9() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: absolute;\\n\"\n    ]);\n    _templateObject9 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject10() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: relative;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-width: 20px;\\n  min-height: 20px;\\n\"\n    ]);\n    _templateObject10 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject11() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0.6);\\n  opacity: 0.4;\\n}\\nto {\\n  transform: scale(1);\\n  opacity: 1;\\n}\"\n    ]);\n    _templateObject11 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject12() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: relative;\\n  transform: scale(0.6);\\n  opacity: 0.4;\\n  min-width: 20px;\\n  animation: \",\n        \" 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)\\n    forwards;\\n\"\n    ]);\n    _templateObject12 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject13() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  display: flex;\\n  align-items: center;\\n  background: #fff;\\n  color: #363636;\\n  line-height: 1.3;\\n  will-change: transform;\\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);\\n  max-width: 350px;\\n  pointer-events: auto;\\n  padding: 8px 10px;\\n  border-radius: 8px;\\n\"\n    ]);\n    _templateObject13 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject14() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  display: flex;\\n  justify-content: center;\\n  margin: 4px 10px;\\n  color: inherit;\\n  flex: 1 1 auto;\\n  white-space: pre-line;\\n\"\n    ]);\n    _templateObject14 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject15() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  z-index: 9999;\\n  > * {\\n    pointer-events: auto;\\n  }\\n\"\n    ]);\n    _templateObject15 = function() {\n        return data;\n    };\n    return data;\n}\nvar _s = $RefreshSig$();\nvar W = (e)=>typeof e == \"function\", f = (e, t)=>W(e) ? e(t) : e;\nvar F = (()=>{\n    let e = 0;\n    return ()=>(++e).toString();\n})(), A = (()=>{\n    let e;\n    return ()=>{\n        if (e === void 0 && typeof window < \"u\") {\n            let t = matchMedia(\"(prefers-reduced-motion: reduce)\");\n            e = !t || t.matches;\n        }\n        return e;\n    };\n})();\n\nvar Y = 20;\nvar U = (e, t)=>{\n    switch(t.type){\n        case 0:\n            return {\n                ...e,\n                toasts: [\n                    t.toast,\n                    ...e.toasts\n                ].slice(0, Y)\n            };\n        case 1:\n            return {\n                ...e,\n                toasts: e.toasts.map((o)=>o.id === t.toast.id ? {\n                        ...o,\n                        ...t.toast\n                    } : o)\n            };\n        case 2:\n            let { toast: r } = t;\n            return U(e, {\n                type: e.toasts.find((o)=>o.id === r.id) ? 1 : 0,\n                toast: r\n            });\n        case 3:\n            let { toastId: s } = t;\n            return {\n                ...e,\n                toasts: e.toasts.map((o)=>o.id === s || s === void 0 ? {\n                        ...o,\n                        dismissed: !0,\n                        visible: !1\n                    } : o)\n            };\n        case 4:\n            return t.toastId === void 0 ? {\n                ...e,\n                toasts: []\n            } : {\n                ...e,\n                toasts: e.toasts.filter((o)=>o.id !== t.toastId)\n            };\n        case 5:\n            return {\n                ...e,\n                pausedAt: t.time\n            };\n        case 6:\n            let a = t.time - (e.pausedAt || 0);\n            return {\n                ...e,\n                pausedAt: void 0,\n                toasts: e.toasts.map((o)=>({\n                        ...o,\n                        pauseDuration: o.pauseDuration + a\n                    }))\n            };\n    }\n}, P = [], y = {\n    toasts: [],\n    pausedAt: void 0\n}, u = (e)=>{\n    y = U(y, e), P.forEach((t)=>{\n        t(y);\n    });\n}, q = {\n    blank: 4e3,\n    error: 4e3,\n    success: 2e3,\n    loading: 1 / 0,\n    custom: 4e3\n}, D = function() {\n    let e = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    let [t, r] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(y), s = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(y);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>(s.current !== y && r(y), P.push(r), ()=>{\n            let o = P.indexOf(r);\n            o > -1 && P.splice(o, 1);\n        }), []);\n    let a = t.toasts.map((o)=>{\n        var n, i, p;\n        return {\n            ...e,\n            ...e[o.type],\n            ...o,\n            removeDelay: o.removeDelay || ((n = e[o.type]) == null ? void 0 : n.removeDelay) || (e == null ? void 0 : e.removeDelay),\n            duration: o.duration || ((i = e[o.type]) == null ? void 0 : i.duration) || (e == null ? void 0 : e.duration) || q[o.type],\n            style: {\n                ...e.style,\n                ...(p = e[o.type]) == null ? void 0 : p.style,\n                ...o.style\n            }\n        };\n    });\n    return {\n        ...t,\n        toasts: a\n    };\n};\nvar J = function(e) {\n    let t = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"blank\", r = arguments.length > 2 ? arguments[2] : void 0;\n    return {\n        createdAt: Date.now(),\n        visible: !0,\n        dismissed: !1,\n        type: t,\n        ariaProps: {\n            role: \"status\",\n            \"aria-live\": \"polite\"\n        },\n        message: e,\n        pauseDuration: 0,\n        ...r,\n        id: (r == null ? void 0 : r.id) || F()\n    };\n}, x = (e)=>(t, r)=>{\n        let s = J(t, e, r);\n        return u({\n            type: 2,\n            toast: s\n        }), s.id;\n    }, c = (e, t)=>x(\"blank\")(e, t);\nc.error = x(\"error\");\nc.success = x(\"success\");\nc.loading = x(\"loading\");\nc.custom = x(\"custom\");\nc.dismiss = (e)=>{\n    u({\n        type: 3,\n        toastId: e\n    });\n};\nc.remove = (e)=>u({\n        type: 4,\n        toastId: e\n    });\nc.promise = (e, t, r)=>{\n    let s = c.loading(t.loading, {\n        ...r,\n        ...r == null ? void 0 : r.loading\n    });\n    return typeof e == \"function\" && (e = e()), e.then((a)=>{\n        let o = t.success ? f(t.success, a) : void 0;\n        return o ? c.success(o, {\n            id: s,\n            ...r,\n            ...r == null ? void 0 : r.success\n        }) : c.dismiss(s), a;\n    }).catch((a)=>{\n        let o = t.error ? f(t.error, a) : void 0;\n        o ? c.error(o, {\n            id: s,\n            ...r,\n            ...r == null ? void 0 : r.error\n        }) : c.dismiss(s);\n    }), e;\n};\n\nvar K = (e, t)=>{\n    u({\n        type: 1,\n        toast: {\n            id: e,\n            height: t\n        }\n    });\n}, X = ()=>{\n    u({\n        type: 5,\n        time: Date.now()\n    });\n}, b = new Map, Z = 1e3, ee = function(e) {\n    let t = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : Z;\n    if (b.has(e)) return;\n    let r = setTimeout(()=>{\n        b.delete(e), u({\n            type: 4,\n            toastId: e\n        });\n    }, t);\n    b.set(e, r);\n}, O = (e)=>{\n    let { toasts: t, pausedAt: r } = D(e);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (r) return;\n        let o = Date.now(), n = t.map((i)=>{\n            if (i.duration === 1 / 0) return;\n            let p = (i.duration || 0) + i.pauseDuration - (o - i.createdAt);\n            if (p < 0) {\n                i.visible && c.dismiss(i.id);\n                return;\n            }\n            return setTimeout(()=>c.dismiss(i.id), p);\n        });\n        return ()=>{\n            n.forEach((i)=>i && clearTimeout(i));\n        };\n    }, [\n        t,\n        r\n    ]);\n    let s = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        r && u({\n            type: 6,\n            time: Date.now()\n        });\n    }, [\n        r\n    ]), a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((o, n)=>{\n        let { reverseOrder: i = !1, gutter: p = 8, defaultPosition: d } = n || {}, h = t.filter((m)=>(m.position || d) === (o.position || d) && m.height), v = h.findIndex((m)=>m.id === o.id), S = h.filter((m, E)=>E < v && m.visible).length;\n        return h.filter((m)=>m.visible).slice(...i ? [\n            S + 1\n        ] : [\n            0,\n            S\n        ]).reduce((m, E)=>m + (E.height || 0) + p, 0);\n    }, [\n        t\n    ]);\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        t.forEach((o)=>{\n            if (o.dismissed) ee(o.id, o.removeDelay);\n            else {\n                let n = b.get(o.id);\n                n && (clearTimeout(n), b.delete(o.id));\n            }\n        });\n    }, [\n        t\n    ]), {\n        toasts: t,\n        handlers: {\n            updateHeight: K,\n            startPause: X,\n            endPause: s,\n            calculateOffset: a\n        }\n    };\n};\n\n\n\n\n\nvar oe = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject()), re = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject1()), se = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject2()), k = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject3(), (e)=>e.primary || \"#ff4b4b\", oe, re, (e)=>e.secondary || \"#fff\", se);\n\nvar ne = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject4()), V = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject5(), (e)=>e.secondary || \"#e0e0e0\", (e)=>e.primary || \"#616161\", ne);\n\nvar pe = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject6()), de = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject7()), _ = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject8(), (e)=>e.primary || \"#61d345\", pe, de, (e)=>e.secondary || \"#fff\");\nvar ue = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject9()), le = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject10()), fe = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject11()), Te = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject12(), fe), M = (param)=>{\n    let { toast: e } = param;\n    let { icon: t, type: r, iconTheme: s } = e;\n    return t !== void 0 ? typeof t == \"string\" ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Te, null, t) : t : r === \"blank\" ? null : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(le, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(V, {\n        ...s\n    }), r !== \"loading\" && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(ue, null, r === \"error\" ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(k, {\n        ...s\n    }) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(_, {\n        ...s\n    })));\n};\nvar ye = (e)=>\"\\n0% {transform: translate3d(0,\".concat(e * -200, \"%,0) scale(.6); opacity:.5;}\\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\\n\"), ge = (e)=>\"\\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\\n100% {transform: translate3d(0,\".concat(e * -150, \"%,-1px) scale(.6); opacity:0;}\\n\"), he = \"0%{opacity:0;} 100%{opacity:1;}\", xe = \"0%{opacity:1;} 100%{opacity:0;}\", be = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject13()), Se = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject14()), Ae = (e, t)=>{\n    let s = e.includes(\"top\") ? 1 : -1, [a, o] = A() ? [\n        he,\n        xe\n    ] : [\n        ye(s),\n        ge(s)\n    ];\n    return {\n        animation: t ? \"\".concat((0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(a), \" 0.35s cubic-bezier(.21,1.02,.73,1) forwards\") : \"\".concat((0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(o), \" 0.4s forwards cubic-bezier(.06,.71,.55,1)\")\n    };\n}, C = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.memo((param)=>{\n    let { toast: e, position: t, style: r, children: s } = param;\n    let a = e.height ? Ae(e.position || t || \"top-center\", e.visible) : {\n        opacity: 0\n    }, o = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(M, {\n        toast: e\n    }), n = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Se, {\n        ...e.ariaProps\n    }, f(e.message, e));\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(be, {\n        className: e.className,\n        style: {\n            ...a,\n            ...r,\n            ...e.style\n        }\n    }, typeof s == \"function\" ? s({\n        icon: o,\n        message: n\n    }) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, o, n));\n});\n\n\n(0,goober__WEBPACK_IMPORTED_MODULE_2__.setup)(react__WEBPACK_IMPORTED_MODULE_1__.createElement);\nvar ve = (param)=>{\n    let { id: e, className: t, style: r, onHeightUpdate: s, children: a } = param;\n    _s();\n    let o = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"ve.useCallback[o]\": (n)=>{\n            if (n) {\n                let i = {\n                    \"ve.useCallback[o].i\": ()=>{\n                        let p = n.getBoundingClientRect().height;\n                        s(e, p);\n                    }\n                }[\"ve.useCallback[o].i\"];\n                i(), new MutationObserver(i).observe(n, {\n                    subtree: !0,\n                    childList: !0,\n                    characterData: !0\n                });\n            }\n        }\n    }[\"ve.useCallback[o]\"], [\n        e,\n        s\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        ref: o,\n        className: t,\n        style: r\n    }, a);\n}, Ee = (e, t)=>{\n    let r = e.includes(\"top\"), s = r ? {\n        top: 0\n    } : {\n        bottom: 0\n    }, a = e.includes(\"center\") ? {\n        justifyContent: \"center\"\n    } : e.includes(\"right\") ? {\n        justifyContent: \"flex-end\"\n    } : {};\n    return {\n        left: 0,\n        right: 0,\n        display: \"flex\",\n        position: \"absolute\",\n        transition: A() ? void 0 : \"all 230ms cubic-bezier(.21,1.02,.73,1)\",\n        transform: \"translateY(\".concat(t * (r ? 1 : -1), \"px)\"),\n        ...s,\n        ...a\n    };\n}, De = (0,goober__WEBPACK_IMPORTED_MODULE_2__.css)(_templateObject15()), R = 16, Oe = (param)=>{\n    let { reverseOrder: e, position: t = \"top-center\", toastOptions: r, gutter: s, children: a, containerStyle: o, containerClassName: n } = param;\n    let { toasts: i, handlers: p } = O(r);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        id: \"_rht_toaster\",\n        style: {\n            position: \"fixed\",\n            zIndex: 9999,\n            top: R,\n            left: R,\n            right: R,\n            bottom: R,\n            pointerEvents: \"none\",\n            ...o\n        },\n        className: n,\n        onMouseEnter: p.startPause,\n        onMouseLeave: p.endPause\n    }, i.map((d)=>{\n        let h = d.position || t, v = p.calculateOffset(d, {\n            reverseOrder: e,\n            gutter: s,\n            defaultPosition: t\n        }), S = Ee(h, v);\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(ve, {\n            id: d.id,\n            key: d.id,\n            onHeightUpdate: p.updateHeight,\n            className: d.visible ? De : \"\",\n            style: S\n        }, d.type === \"custom\" ? f(d.message, d) : a ? a(d) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(C, {\n            toast: d,\n            position: h\n        }));\n    }));\n};\n_s(ve, \"LQ34HCRCKbaP7NB9wB8OQNidTak=\");\nvar Vt = c;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/payment/page.tsx":
/*!**********************************!*\
  !*** ./src/app/payment/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PaymentPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_stripe_plans__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/stripe/plans */ \"(app-pages-browser)/./src/lib/stripe/plans.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n// ===== Archivo: src\\app\\payment\\page.tsx =====\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction PaymentContent() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const planId = searchParams.get('plan') || 'free';\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [confirmPassword, setConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [customerName, setCustomerName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const plan = (0,_lib_stripe_plans__WEBPACK_IMPORTED_MODULE_3__.getPlanById)(planId);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PaymentContent.useEffect\": ()=>{\n            if (!plan) {\n                router.push('/'); // Redirigir si el plan no es válido\n            }\n        }\n    }[\"PaymentContent.useEffect\"], [\n        plan,\n        router\n    ]);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Validaciones básicas\n        if (!email.trim()) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('Por favor, ingresa tu email');\n            return;\n        }\n        // Solo validar contraseña para planes de pago\n        if (planId !== 'free') {\n            if (!password.trim()) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('Por favor, ingresa una contraseña');\n                return;\n            }\n            if (password.length < 6) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('La contraseña debe tener al menos 6 caracteres');\n                return;\n            }\n            if (password !== confirmPassword) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('Las contraseñas no coinciden');\n                return;\n            }\n        }\n        setIsLoading(true);\n        try {\n            // Para el plan gratuito\n            if (planId === 'free') {\n                // Llamar al endpoint de registro gratuito (sin contraseña)\n                const registerResponse = await fetch('/api/auth/register-free', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        email,\n                        customerName\n                    })\n                });\n                const registerData = await registerResponse.json();\n                if (registerResponse.ok && registerData.success) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success('¡Casi listo! Revisa tu email para establecer tu contraseña y activar tu cuenta.');\n                    router.push(\"/thank-you?plan=\".concat(planId, \"&email_sent=true\"));\n                } else {\n                    // Manejo de errores del endpoint de registro\n                    if (registerResponse.status === 429) {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('Demasiados intentos. Inténtalo en 15 minutos.');\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(registerData.error || 'Error al crear la cuenta gratuita');\n                    }\n                }\n            } else {\n                // NUEVO FLUJO: Para planes de pago, crear usuario primero y luego ir a Stripe\n                console.log('🔄 Iniciando nuevo flujo de pre-registro para plan de pago');\n                // Paso 1: Pre-registrar usuario\n                const preRegisterResponse = await fetch('/api/auth/pre-register-paid', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        email,\n                        password,\n                        customerName: customerName || email.split('@')[0],\n                        planId\n                    })\n                });\n                const preRegisterData = await preRegisterResponse.json();\n                if (!preRegisterResponse.ok) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(preRegisterData.error || 'Error al crear la cuenta');\n                    return;\n                }\n                console.log('✅ Usuario pre-registrado exitosamente:', preRegisterData.userId);\n                // Paso 2: Crear sesión de Stripe con el userId\n                const stripeResponse = await fetch('/api/stripe/create-checkout-session', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        planId,\n                        email,\n                        customerName,\n                        userId: preRegisterData.userId\n                    })\n                });\n                const stripeData = await stripeResponse.json();\n                if (stripeResponse.ok && stripeData.url) {\n                    console.log('🔄 Redirigiendo a Stripe Checkout...');\n                    window.location.href = stripeData.url; // Redirigir a Stripe Checkout\n                } else {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(stripeData.error || 'Error al crear la sesión de pago');\n                }\n            }\n        } catch (error) {\n            console.error('Error en handleSubmit:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('Error al procesar la solicitud. Por favor, intenta de nuevo.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (!plan) {\n        // Este return se activará si el useEffect redirige, o si el plan es inválido inicialmente\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: \"Cargando detalles del plan o redirigiendo...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n            lineNumber: 146,\n            columnNumber: 9\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: plan.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-2xl font-semibold text-blue-600 mt-2\",\n                            children: [\n                                plan.price === 0 ? 'Gratis' : \"€\".concat((plan.price / 100).toFixed(2)),\n                                (planId === 'pro' || planId === 'usuario') && plan.price > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"/mes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 78\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-3\",\n                                    children: \"Caracter\\xedsticas del plan:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: plan.features.map((feature, index)=>{\n                                        // Si es un encabezado (Incluye: o No incluye:) - SIN ICONO\n                                        if (feature === 'Incluye:' || feature === 'No incluye:') {\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"mt-4 first:mt-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold text-gray-900\",\n                                                    children: feature\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 21\n                                            }, this);\n                                        }\n                                        // Lógica mejorada para determinar si un ítem está bajo \"No incluye:\"\n                                        const isNotIncludedItem = (()=>{\n                                            if (!feature.startsWith('• ')) return false;\n                                            // Buscar hacia atrás el encabezado más cercano\n                                            for(let i = index - 1; i >= 0; i--){\n                                                if (plan.features[i] === 'Incluye:') return false;\n                                                if (plan.features[i] === 'No incluye:') return true;\n                                            }\n                                            return false;\n                                        })();\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start ml-2\",\n                                            children: [\n                                                feature.startsWith('• ') ? isNotIncludedItem ? // Icono de Cruz Roja para características no incluidas\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-4 w-4 mt-0.5 mr-3 flex-shrink-0 text-red-500\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 25\n                                                }, this) : // Icono de Check Verde para características incluidas\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-4 w-4 mt-0.5 mr-3 flex-shrink-0 text-green-500\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 25\n                                                }, this) : null,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: feature.startsWith('• ') ? feature.substring(2) : feature\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Email *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            id: \"email\",\n                                            required: true,\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value),\n                                            className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            placeholder: \"<EMAIL>\",\n                                            disabled: isLoading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this),\n                                planId !== 'free' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"password\",\n                                                    className: \"block text-sm font-medium text-gray-700\",\n                                                    children: \"Contrase\\xf1a *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"password\",\n                                                    id: \"password\",\n                                                    required: true,\n                                                    value: password,\n                                                    onChange: (e)=>setPassword(e.target.value),\n                                                    className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                    placeholder: \"M\\xednimo 6 caracteres\",\n                                                    disabled: isLoading,\n                                                    minLength: 6\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"confirmPassword\",\n                                                    className: \"block text-sm font-medium text-gray-700\",\n                                                    children: \"Confirmar Contrase\\xf1a *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"password\",\n                                                    id: \"confirmPassword\",\n                                                    required: true,\n                                                    value: confirmPassword,\n                                                    onChange: (e)=>setConfirmPassword(e.target.value),\n                                                    className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                    placeholder: \"Repite tu contrase\\xf1a\",\n                                                    disabled: isLoading,\n                                                    minLength: 6\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                planId === 'free' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-50 border border-blue-200 rounded-md p-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-700\",\n                                        children: \"\\uD83D\\uDCE7 Te enviaremos un email para que establezcas tu contrase\\xf1a y actives tu cuenta.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"customerName\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Nombre (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"customerName\",\n                                            value: customerName,\n                                            onChange: (e)=>setCustomerName(e.target.value),\n                                            className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            placeholder: \"Tu nombre\",\n                                            disabled: isLoading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: isLoading ? 'Procesando...' : planId === 'free' ? 'Solicitar Acceso Gratuito' : 'Proceder al Pago'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n            lineNumber: 154,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n_s(PaymentContent, \"XkvHlsYN6OWBkX73yA7n4AKHYs8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = PaymentContent;\nfunction PaymentPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 15\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-4 text-gray-600\",\n                                children: \"Cargando...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 15\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                lineNumber: 332,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n            lineNumber: 331,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PaymentContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n            lineNumber: 342,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n        lineNumber: 330,\n        columnNumber: 5\n    }, this);\n}\n_c1 = PaymentPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"PaymentContent\");\n$RefreshReg$(_c1, \"PaymentPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/payment/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/config/constants.ts":
/*!*********************************!*\
  !*** ./src/config/constants.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_URLS: () => (/* binding */ APP_URLS),\n/* harmony export */   AUTHENTICATED_ROUTES: () => (/* binding */ AUTHENTICATED_ROUTES),\n/* harmony export */   AUTOMATION_CONFIG: () => (/* binding */ AUTOMATION_CONFIG),\n/* harmony export */   ERROR_MESSAGES: () => (/* binding */ ERROR_MESSAGES),\n/* harmony export */   FILE_LIMITS: () => (/* binding */ FILE_LIMITS),\n/* harmony export */   FREE_PLAN_LIMITS: () => (/* binding */ FREE_PLAN_LIMITS),\n/* harmony export */   NOTIFICATION_TYPES: () => (/* binding */ NOTIFICATION_TYPES),\n/* harmony export */   PAYMENT_STATES: () => (/* binding */ PAYMENT_STATES),\n/* harmony export */   PRICING: () => (/* binding */ PRICING),\n/* harmony export */   PROCESSING_STATES: () => (/* binding */ PROCESSING_STATES),\n/* harmony export */   PUBLIC_ROUTES: () => (/* binding */ PUBLIC_ROUTES),\n/* harmony export */   RATE_LIMITS: () => (/* binding */ RATE_LIMITS),\n/* harmony export */   REQUIRED_ENV_VARS: () => (/* binding */ REQUIRED_ENV_VARS),\n/* harmony export */   RETRY_CONFIG: () => (/* binding */ RETRY_CONFIG),\n/* harmony export */   SECURITY_CONFIG: () => (/* binding */ SECURITY_CONFIG),\n/* harmony export */   SECURITY_RISK_SCORES: () => (/* binding */ SECURITY_RISK_SCORES),\n/* harmony export */   SEVERITY_LEVELS: () => (/* binding */ SEVERITY_LEVELS),\n/* harmony export */   SUCCESS_MESSAGES: () => (/* binding */ SUCCESS_MESSAGES),\n/* harmony export */   TEXT_LIMITS: () => (/* binding */ TEXT_LIMITS),\n/* harmony export */   TIMEOUTS: () => (/* binding */ TIMEOUTS),\n/* harmony export */   TOKEN_LIMITS: () => (/* binding */ TOKEN_LIMITS),\n/* harmony export */   VALIDATION_MESSAGES: () => (/* binding */ VALIDATION_MESSAGES)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// src/config/constants.ts\n// Constantes centralizadas del sistema\n// ============================================================================\n// CONSTANTES DE APLICACIÓN\n// ============================================================================\n/**\n * URLs y rutas de la aplicación\n */ const APP_URLS = {\n    BASE: \"http://localhost:3001\" || 0,\n    SITE: \"http://localhost:3001\" || 0,\n    UPGRADE_PLAN: '/upgrade-plan',\n    THANK_YOU: '/thank-you',\n    LOGIN: '/login',\n    DASHBOARD: '/app',\n    PROFILE: '/profile',\n    WELCOME: '/welcome'\n};\n/**\n * Rutas públicas (no requieren autenticación)\n */ const PUBLIC_ROUTES = [\n    '/',\n    '/login',\n    '/payment',\n    '/thank-you',\n    '/contact',\n    '/privacy',\n    '/terms',\n    '/auth/callback',\n    '/auth/confirmed',\n    '/auth/unauthorized',\n    '/auth/reset-password',\n    '/auth/confirm-reset',\n    '/api/auth/register-free',\n    '/api/auth/pre-register-paid',\n    '/api/stripe/webhook',\n    '/api/stripe/create-checkout-session',\n    '/api/stripe/create-token-checkout',\n    '/api/notify-signup',\n    '/api/user/status',\n    '/api/health',\n    '/api/auth/initiate-password-setup'\n];\n/**\n * Rutas que requieren autenticación básica\n */ const AUTHENTICATED_ROUTES = [\n    '/app',\n    '/dashboard',\n    '/profile',\n    '/welcome',\n    '/upgrade-plan'\n];\n// ============================================================================\n// CONSTANTES DE VALIDACIÓN Y LÍMITES\n// ============================================================================\n/**\n * Límites de archivos y uploads\n */ const FILE_LIMITS = {\n    MAX_SIZE_MB: 5,\n    MAX_SIZE_BYTES: 5 * 1024 * 1024,\n    ALLOWED_TYPES: [\n        'application/pdf'\n    ],\n    ALLOWED_EXTENSIONS: [\n        '.pdf'\n    ]\n};\n/**\n * Límites de texto y contenido\n */ const TEXT_LIMITS = {\n    MIN_PASSWORD_LENGTH: 8,\n    MAX_PASSWORD_LENGTH: 128,\n    MAX_DOCUMENT_TITLE_LENGTH: 200,\n    MAX_DESCRIPTION_LENGTH: 500,\n    MIN_CONTENT_LENGTH: 100,\n    MAX_CONTENT_LENGTH: 50000\n};\n/**\n * Límites de tokens y uso\n */ const TOKEN_LIMITS = {\n    DEFAULT_FREE_LIMIT: 50000,\n    WARNING_THRESHOLD_PERCENTAGE: 80,\n    CRITICAL_THRESHOLD_PERCENTAGE: 90,\n    EXCEEDED_THRESHOLD_PERCENTAGE: 100\n};\n/**\n * Límites de rate limiting\n */ const RATE_LIMITS = {\n    DEFAULT_WINDOW_MINUTES: 60,\n    DEFAULT_MAX_REQUESTS: 100,\n    API_REQUESTS_PER_MINUTE: 60,\n    UPLOAD_REQUESTS_PER_HOUR: 10,\n    AUTH_ATTEMPTS_PER_HOUR: 5\n};\n// ============================================================================\n// CONSTANTES DE TIEMPO Y CONFIGURACIÓN\n// ============================================================================\n/**\n * Timeouts y intervalos\n */ const TIMEOUTS = {\n    SESSION_TIMEOUT_MS: 5 * 60 * 1000,\n    API_TIMEOUT_MS: 30 * 1000,\n    UPLOAD_TIMEOUT_MS: 60 * 1000,\n    RETRY_DELAY_MS: 1000,\n    POLLING_INTERVAL_MS: 2000 // 2 segundos\n};\n/**\n * Configuración de reintentos\n */ const RETRY_CONFIG = {\n    MAX_ATTEMPTS: 3,\n    BACKOFF_MULTIPLIER: 2,\n    INITIAL_DELAY_MS: 1000\n};\n/**\n * Configuración de seguridad\n */ const SECURITY_CONFIG = {\n    ENABLE_STRICT_VALIDATION: process.env.STRICT_PLAN_VALIDATION === 'true',\n    REQUIRE_PAYMENT_VERIFICATION: process.env.REQUIRE_PAYMENT_VERIFICATION === 'true',\n    ENABLE_ACCESS_LOGGING: process.env.ENABLE_ACCESS_LOGGING === 'true',\n    ENABLE_FEATURE_VALIDATION: process.env.ENABLE_FEATURE_VALIDATION === 'true',\n    AUTO_ACTIVATE_PAYMENTS: process.env.AUTO_ACTIVATE_PAYMENTS === 'true',\n    ENABLE_PUBLIC_SIGNUP: process.env.ENABLE_PUBLIC_SIGNUP === 'true'\n};\n// ============================================================================\n// CONSTANTES DE MENSAJES Y TEXTOS\n// ============================================================================\n/**\n * Mensajes de error comunes\n */ const ERROR_MESSAGES = {\n    UNAUTHORIZED: 'No autorizado',\n    FORBIDDEN: 'Acceso denegado',\n    NOT_FOUND: 'Recurso no encontrado',\n    INTERNAL_ERROR: 'Error interno del servidor',\n    INVALID_DATA: 'Datos inválidos',\n    USER_NOT_FOUND: 'Usuario no encontrado',\n    PROFILE_NOT_FOUND: 'Perfil de usuario no encontrado',\n    PAYMENT_REQUIRED: 'Pago requerido',\n    LIMIT_EXCEEDED: 'Límite excedido',\n    FILE_TOO_LARGE: 'El archivo es demasiado grande',\n    INVALID_FILE_TYPE: 'Tipo de archivo no válido',\n    UPLOAD_FAILED: 'Error al subir el archivo',\n    PROCESSING_ERROR: 'Error al procesar la solicitud',\n    NETWORK_ERROR: 'Error de conexión',\n    TIMEOUT_ERROR: 'Tiempo de espera agotado'\n};\n/**\n * Mensajes de éxito\n */ const SUCCESS_MESSAGES = {\n    UPLOAD_SUCCESS: 'Archivo subido correctamente',\n    SAVE_SUCCESS: 'Guardado correctamente',\n    UPDATE_SUCCESS: 'Actualizado correctamente',\n    DELETE_SUCCESS: 'Eliminado correctamente',\n    PAYMENT_SUCCESS: 'Pago procesado correctamente',\n    REGISTRATION_SUCCESS: 'Registro completado',\n    LOGIN_SUCCESS: 'Sesión iniciada',\n    LOGOUT_SUCCESS: 'Sesión cerrada',\n    PASSWORD_RESET_SUCCESS: 'Contraseña restablecida',\n    EMAIL_SENT: 'Email enviado correctamente'\n};\n/**\n * Mensajes de validación\n */ const VALIDATION_MESSAGES = {\n    REQUIRED_FIELD: 'Este campo es obligatorio',\n    INVALID_EMAIL: 'Email no válido',\n    PASSWORD_TOO_SHORT: \"La contrase\\xf1a debe tener al menos \".concat(TEXT_LIMITS.MIN_PASSWORD_LENGTH, \" caracteres\"),\n    PASSWORD_TOO_LONG: \"La contrase\\xf1a no puede tener m\\xe1s de \".concat(TEXT_LIMITS.MAX_PASSWORD_LENGTH, \" caracteres\"),\n    PASSWORDS_DONT_MATCH: 'Las contraseñas no coinciden',\n    INVALID_FILE_SIZE: \"El archivo no puede superar \".concat(FILE_LIMITS.MAX_SIZE_MB, \"MB\"),\n    INVALID_FILE_FORMAT: 'Formato de archivo no válido',\n    TEXT_TOO_SHORT: \"El texto debe tener al menos \".concat(TEXT_LIMITS.MIN_CONTENT_LENGTH, \" caracteres\"),\n    TEXT_TOO_LONG: \"El texto no puede superar \".concat(TEXT_LIMITS.MAX_CONTENT_LENGTH, \" caracteres\")\n};\n// ============================================================================\n// CONSTANTES DE ESTADO Y TIPOS\n// ============================================================================\n/**\n * Estados de procesamiento\n */ const PROCESSING_STATES = {\n    IDLE: 'idle',\n    LOADING: 'loading',\n    PROCESSING: 'processing',\n    SUCCESS: 'success',\n    ERROR: 'error',\n    CANCELLED: 'cancelled'\n};\n/**\n * Estados de pago\n */ const PAYMENT_STATES = {\n    PENDING: 'pending',\n    PROCESSING: 'processing',\n    COMPLETED: 'completed',\n    FAILED: 'failed',\n    CANCELLED: 'cancelled',\n    REFUNDED: 'refunded'\n};\n/**\n * Tipos de notificación\n */ const NOTIFICATION_TYPES = {\n    INFO: 'info',\n    SUCCESS: 'success',\n    WARNING: 'warning',\n    ERROR: 'error'\n};\n/**\n * Niveles de severidad\n */ const SEVERITY_LEVELS = {\n    LOW: 'low',\n    MEDIUM: 'medium',\n    HIGH: 'high',\n    CRITICAL: 'critical'\n};\n// ============================================================================\n// CONSTANTES DE CONFIGURACIÓN DE ENTORNO\n// ============================================================================\n/**\n * Variables de entorno requeridas\n */ const REQUIRED_ENV_VARS = [\n    'NEXT_PUBLIC_SUPABASE_URL',\n    'NEXT_PUBLIC_SUPABASE_ANON_KEY',\n    'SUPABASE_SERVICE_ROLE_KEY',\n    'STRIPE_SECRET_KEY',\n    'STRIPE_WEBHOOK_SECRET',\n    'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY'\n];\n/**\n * Configuración de automatización\n */ const AUTOMATION_CONFIG = {\n    INVITATION_EXPIRY_HOURS: parseInt(process.env.INVITATION_EXPIRY_HOURS || '24'),\n    DEFAULT_TRIAL_DAYS: 5,\n    DEFAULT_GRACE_PERIOD_DAYS: 3\n};\n// ============================================================================\n// CONSTANTES DE BUSINESS LOGIC\n// ============================================================================\n/**\n * Costos y precios (en centavos)\n */ const PRICING = {\n    ADDITIONAL_TOKENS_PRICE: 1000,\n    ADDITIONAL_TOKENS_AMOUNT: 1000000,\n    FREE_PLAN_PRICE: 0,\n    BASIC_PLAN_PRICE: 999,\n    PRO_PLAN_PRICE: 1999 // €19.99\n};\n/**\n * Límites de planes gratuitos\n */ const FREE_PLAN_LIMITS = {\n    DOCUMENTS: 1,\n    MIND_MAPS_TRIAL: 2,\n    TESTS_TRIAL: 10,\n    FLASHCARDS_TRIAL: 10,\n    TOKENS_TRIAL: 50000,\n    TRIAL_DAYS: 5\n};\n/**\n * Factores de riesgo de seguridad\n */ const SECURITY_RISK_SCORES = {\n    MISSING_USER_AGENT: 30,\n    BOT_USER_AGENT: 20,\n    EXTERNAL_REFERER: 10,\n    SUSPICIOUS_PATTERN: 25,\n    HIGH_FREQUENCY: 40\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb25maWcvY29uc3RhbnRzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsMEJBQTBCO0FBQzFCLHVDQUF1QztBQUV2QywrRUFBK0U7QUFDL0UsMkJBQTJCO0FBQzNCLCtFQUErRTtBQUUvRTs7Q0FFQyxHQUNNLE1BQU1BLFdBQVc7SUFDdEJDLE1BQU1DLHVCQUErQixJQUFJLENBQXVCO0lBQ2hFRyxNQUFNSCx1QkFBZ0MsSUFBSSxDQUF1QjtJQUNqRUssY0FBYztJQUNkQyxXQUFXO0lBQ1hDLE9BQU87SUFDUEMsV0FBVztJQUNYQyxTQUFTO0lBQ1RDLFNBQVM7QUFDWCxFQUFXO0FBRVg7O0NBRUMsR0FDTSxNQUFNQyxnQkFBZ0I7SUFDM0I7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0NBQ0QsQ0FBVTtBQUVYOztDQUVDLEdBQ00sTUFBTUMsdUJBQXVCO0lBQ2xDO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7Q0FDRCxDQUFVO0FBRVgsK0VBQStFO0FBQy9FLHFDQUFxQztBQUNyQywrRUFBK0U7QUFFL0U7O0NBRUMsR0FDTSxNQUFNQyxjQUFjO0lBQ3pCQyxhQUFhO0lBQ2JDLGdCQUFnQixJQUFJLE9BQU87SUFDM0JDLGVBQWU7UUFBQztLQUFrQjtJQUNsQ0Msb0JBQW9CO1FBQUM7S0FBTztBQUM5QixFQUFXO0FBRVg7O0NBRUMsR0FDTSxNQUFNQyxjQUFjO0lBQ3pCQyxxQkFBcUI7SUFDckJDLHFCQUFxQjtJQUNyQkMsMkJBQTJCO0lBQzNCQyx3QkFBd0I7SUFDeEJDLG9CQUFvQjtJQUNwQkMsb0JBQW9CO0FBQ3RCLEVBQVc7QUFFWDs7Q0FFQyxHQUNNLE1BQU1DLGVBQWU7SUFDMUJDLG9CQUFvQjtJQUNwQkMsOEJBQThCO0lBQzlCQywrQkFBK0I7SUFDL0JDLCtCQUErQjtBQUNqQyxFQUFXO0FBRVg7O0NBRUMsR0FDTSxNQUFNQyxjQUFjO0lBQ3pCQyx3QkFBd0I7SUFDeEJDLHNCQUFzQjtJQUN0QkMseUJBQXlCO0lBQ3pCQywwQkFBMEI7SUFDMUJDLHdCQUF3QjtBQUMxQixFQUFXO0FBRVgsK0VBQStFO0FBQy9FLHVDQUF1QztBQUN2QywrRUFBK0U7QUFFL0U7O0NBRUMsR0FDTSxNQUFNQyxXQUFXO0lBQ3RCQyxvQkFBb0IsSUFBSSxLQUFLO0lBQzdCQyxnQkFBZ0IsS0FBSztJQUNyQkMsbUJBQW1CLEtBQUs7SUFDeEJDLGdCQUFnQjtJQUNoQkMscUJBQXFCLEtBQUssYUFBYTtBQUN6QyxFQUFXO0FBRVg7O0NBRUMsR0FDTSxNQUFNQyxlQUFlO0lBQzFCQyxjQUFjO0lBQ2RDLG9CQUFvQjtJQUNwQkMsa0JBQWtCO0FBQ3BCLEVBQVc7QUFFWDs7Q0FFQyxHQUNNLE1BQU1DLGtCQUFrQjtJQUM3QkMsMEJBQTBCL0MsT0FBT0EsQ0FBQ0MsR0FBRyxDQUFDK0Msc0JBQXNCLEtBQUs7SUFDakVDLDhCQUE4QmpELE9BQU9BLENBQUNDLEdBQUcsQ0FBQ2dELDRCQUE0QixLQUFLO0lBQzNFQyx1QkFBdUJsRCxPQUFPQSxDQUFDQyxHQUFHLENBQUNpRCxxQkFBcUIsS0FBSztJQUM3REMsMkJBQTJCbkQsT0FBT0EsQ0FBQ0MsR0FBRyxDQUFDa0QseUJBQXlCLEtBQUs7SUFDckVDLHdCQUF3QnBELE9BQU9BLENBQUNDLEdBQUcsQ0FBQ21ELHNCQUFzQixLQUFLO0lBQy9EQyxzQkFBc0JyRCxPQUFPQSxDQUFDQyxHQUFHLENBQUNvRCxvQkFBb0IsS0FBSztBQUM3RCxFQUFXO0FBRVgsK0VBQStFO0FBQy9FLGtDQUFrQztBQUNsQywrRUFBK0U7QUFFL0U7O0NBRUMsR0FDTSxNQUFNQyxpQkFBaUI7SUFDNUJDLGNBQWM7SUFDZEMsV0FBVztJQUNYQyxXQUFXO0lBQ1hDLGdCQUFnQjtJQUNoQkMsY0FBYztJQUNkQyxnQkFBZ0I7SUFDaEJDLG1CQUFtQjtJQUNuQkMsa0JBQWtCO0lBQ2xCQyxnQkFBZ0I7SUFDaEJDLGdCQUFnQjtJQUNoQkMsbUJBQW1CO0lBQ25CQyxlQUFlO0lBQ2ZDLGtCQUFrQjtJQUNsQkMsZUFBZTtJQUNmQyxlQUFlO0FBQ2pCLEVBQVc7QUFFWDs7Q0FFQyxHQUNNLE1BQU1DLG1CQUFtQjtJQUM5QkMsZ0JBQWdCO0lBQ2hCQyxjQUFjO0lBQ2RDLGdCQUFnQjtJQUNoQkMsZ0JBQWdCO0lBQ2hCQyxpQkFBaUI7SUFDakJDLHNCQUFzQjtJQUN0QkMsZUFBZTtJQUNmQyxnQkFBZ0I7SUFDaEJDLHdCQUF3QjtJQUN4QkMsWUFBWTtBQUNkLEVBQVc7QUFFWDs7Q0FFQyxHQUNNLE1BQU1DLHNCQUFzQjtJQUNqQ0MsZ0JBQWdCO0lBQ2hCQyxlQUFlO0lBQ2ZDLG9CQUFvQix3Q0FBcUUsT0FBaENsRSxZQUFZQyxtQkFBbUIsRUFBQztJQUN6RmtFLG1CQUFtQiw2Q0FBdUUsT0FBaENuRSxZQUFZRSxtQkFBbUIsRUFBQztJQUMxRmtFLHNCQUFzQjtJQUN0QkMsbUJBQW1CLCtCQUF1RCxPQUF4QjFFLFlBQVlDLFdBQVcsRUFBQztJQUMxRTBFLHFCQUFxQjtJQUNyQkMsZ0JBQWdCLGdDQUErRCxPQUEvQnZFLFlBQVlLLGtCQUFrQixFQUFDO0lBQy9FbUUsZUFBZSw2QkFBNEQsT0FBL0J4RSxZQUFZTSxrQkFBa0IsRUFBQztBQUM3RSxFQUFXO0FBRVgsK0VBQStFO0FBQy9FLCtCQUErQjtBQUMvQiwrRUFBK0U7QUFFL0U7O0NBRUMsR0FDTSxNQUFNbUUsb0JBQW9CO0lBQy9CQyxNQUFNO0lBQ05DLFNBQVM7SUFDVEMsWUFBWTtJQUNaQyxTQUFTO0lBQ1RDLE9BQU87SUFDUEMsV0FBVztBQUNiLEVBQVc7QUFFWDs7Q0FFQyxHQUNNLE1BQU1DLGlCQUFpQjtJQUM1QkMsU0FBUztJQUNUTCxZQUFZO0lBQ1pNLFdBQVc7SUFDWEMsUUFBUTtJQUNSSixXQUFXO0lBQ1hLLFVBQVU7QUFDWixFQUFXO0FBRVg7O0NBRUMsR0FDTSxNQUFNQyxxQkFBcUI7SUFDaENDLE1BQU07SUFDTlQsU0FBUztJQUNUVSxTQUFTO0lBQ1RULE9BQU87QUFDVCxFQUFXO0FBRVg7O0NBRUMsR0FDTSxNQUFNVSxrQkFBa0I7SUFDN0JDLEtBQUs7SUFDTEMsUUFBUTtJQUNSQyxNQUFNO0lBQ05DLFVBQVU7QUFDWixFQUFXO0FBRVgsK0VBQStFO0FBQy9FLHlDQUF5QztBQUN6QywrRUFBK0U7QUFFL0U7O0NBRUMsR0FDTSxNQUFNQyxvQkFBb0I7SUFDL0I7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0NBQ0QsQ0FBVTtBQUVYOztDQUVDLEdBQ00sTUFBTUMsb0JBQW9CO0lBQy9CQyx5QkFBeUJDLFNBQVNsSCxPQUFPQSxDQUFDQyxHQUFHLENBQUNnSCx1QkFBdUIsSUFBSTtJQUN6RUUsb0JBQW9CO0lBQ3BCQywyQkFBMkI7QUFDN0IsRUFBVztBQUVYLCtFQUErRTtBQUMvRSwrQkFBK0I7QUFDL0IsK0VBQStFO0FBRS9FOztDQUVDLEdBQ00sTUFBTUMsVUFBVTtJQUNyQkMseUJBQXlCO0lBQ3pCQywwQkFBMEI7SUFDMUJDLGlCQUFpQjtJQUNqQkMsa0JBQWtCO0lBQ2xCQyxnQkFBZ0IsS0FBSyxTQUFTO0FBQ2hDLEVBQVc7QUFFWDs7Q0FFQyxHQUNNLE1BQU1DLG1CQUFtQjtJQUM5QkMsV0FBVztJQUNYQyxpQkFBaUI7SUFDakJDLGFBQWE7SUFDYkMsa0JBQWtCO0lBQ2xCQyxjQUFjO0lBQ2RDLFlBQVk7QUFDZCxFQUFXO0FBRVg7O0NBRUMsR0FDTSxNQUFNQyx1QkFBdUI7SUFDbENDLG9CQUFvQjtJQUNwQkMsZ0JBQWdCO0lBQ2hCQyxrQkFBa0I7SUFDbEJDLG9CQUFvQjtJQUNwQkMsZ0JBQWdCO0FBQ2xCLEVBQVciLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXHYxNlxcc3JjXFxjb25maWdcXGNvbnN0YW50cy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvY29uZmlnL2NvbnN0YW50cy50c1xuLy8gQ29uc3RhbnRlcyBjZW50cmFsaXphZGFzIGRlbCBzaXN0ZW1hXG5cbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbi8vIENPTlNUQU5URVMgREUgQVBMSUNBQ0nDk05cbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cblxuLyoqXG4gKiBVUkxzIHkgcnV0YXMgZGUgbGEgYXBsaWNhY2nDs25cbiAqL1xuZXhwb3J0IGNvbnN0IEFQUF9VUkxTID0ge1xuICBCQVNFOiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUFBfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjMwMDAnLFxuICBTSVRFOiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TSVRFX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDozMDAwJyxcbiAgVVBHUkFERV9QTEFOOiAnL3VwZ3JhZGUtcGxhbicsXG4gIFRIQU5LX1lPVTogJy90aGFuay15b3UnLFxuICBMT0dJTjogJy9sb2dpbicsXG4gIERBU0hCT0FSRDogJy9hcHAnLFxuICBQUk9GSUxFOiAnL3Byb2ZpbGUnLFxuICBXRUxDT01FOiAnL3dlbGNvbWUnXG59IGFzIGNvbnN0O1xuXG4vKipcbiAqIFJ1dGFzIHDDumJsaWNhcyAobm8gcmVxdWllcmVuIGF1dGVudGljYWNpw7NuKVxuICovXG5leHBvcnQgY29uc3QgUFVCTElDX1JPVVRFUyA9IFtcbiAgJy8nLFxuICAnL2xvZ2luJyxcbiAgJy9wYXltZW50JyxcbiAgJy90aGFuay15b3UnLFxuICAnL2NvbnRhY3QnLFxuICAnL3ByaXZhY3knLFxuICAnL3Rlcm1zJyxcbiAgJy9hdXRoL2NhbGxiYWNrJyxcbiAgJy9hdXRoL2NvbmZpcm1lZCcsXG4gICcvYXV0aC91bmF1dGhvcml6ZWQnLFxuICAnL2F1dGgvcmVzZXQtcGFzc3dvcmQnLFxuICAnL2F1dGgvY29uZmlybS1yZXNldCcsXG4gICcvYXBpL2F1dGgvcmVnaXN0ZXItZnJlZScsXG4gICcvYXBpL2F1dGgvcHJlLXJlZ2lzdGVyLXBhaWQnLFxuICAnL2FwaS9zdHJpcGUvd2ViaG9vaycsXG4gICcvYXBpL3N0cmlwZS9jcmVhdGUtY2hlY2tvdXQtc2Vzc2lvbicsXG4gICcvYXBpL3N0cmlwZS9jcmVhdGUtdG9rZW4tY2hlY2tvdXQnLFxuICAnL2FwaS9ub3RpZnktc2lnbnVwJyxcbiAgJy9hcGkvdXNlci9zdGF0dXMnLFxuICAnL2FwaS9oZWFsdGgnLFxuICAnL2FwaS9hdXRoL2luaXRpYXRlLXBhc3N3b3JkLXNldHVwJ1xuXSBhcyBjb25zdDtcblxuLyoqXG4gKiBSdXRhcyBxdWUgcmVxdWllcmVuIGF1dGVudGljYWNpw7NuIGLDoXNpY2FcbiAqL1xuZXhwb3J0IGNvbnN0IEFVVEhFTlRJQ0FURURfUk9VVEVTID0gW1xuICAnL2FwcCcsXG4gICcvZGFzaGJvYXJkJyxcbiAgJy9wcm9maWxlJyxcbiAgJy93ZWxjb21lJyxcbiAgJy91cGdyYWRlLXBsYW4nXG5dIGFzIGNvbnN0O1xuXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4vLyBDT05TVEFOVEVTIERFIFZBTElEQUNJw5NOIFkgTMONTUlURVNcbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cblxuLyoqXG4gKiBMw61taXRlcyBkZSBhcmNoaXZvcyB5IHVwbG9hZHNcbiAqL1xuZXhwb3J0IGNvbnN0IEZJTEVfTElNSVRTID0ge1xuICBNQVhfU0laRV9NQjogNSxcbiAgTUFYX1NJWkVfQllURVM6IDUgKiAxMDI0ICogMTAyNCxcbiAgQUxMT1dFRF9UWVBFUzogWydhcHBsaWNhdGlvbi9wZGYnXSxcbiAgQUxMT1dFRF9FWFRFTlNJT05TOiBbJy5wZGYnXVxufSBhcyBjb25zdDtcblxuLyoqXG4gKiBMw61taXRlcyBkZSB0ZXh0byB5IGNvbnRlbmlkb1xuICovXG5leHBvcnQgY29uc3QgVEVYVF9MSU1JVFMgPSB7XG4gIE1JTl9QQVNTV09SRF9MRU5HVEg6IDgsXG4gIE1BWF9QQVNTV09SRF9MRU5HVEg6IDEyOCxcbiAgTUFYX0RPQ1VNRU5UX1RJVExFX0xFTkdUSDogMjAwLFxuICBNQVhfREVTQ1JJUFRJT05fTEVOR1RIOiA1MDAsXG4gIE1JTl9DT05URU5UX0xFTkdUSDogMTAwLFxuICBNQVhfQ09OVEVOVF9MRU5HVEg6IDUwMDAwXG59IGFzIGNvbnN0O1xuXG4vKipcbiAqIEzDrW1pdGVzIGRlIHRva2VucyB5IHVzb1xuICovXG5leHBvcnQgY29uc3QgVE9LRU5fTElNSVRTID0ge1xuICBERUZBVUxUX0ZSRUVfTElNSVQ6IDUwMDAwLFxuICBXQVJOSU5HX1RIUkVTSE9MRF9QRVJDRU5UQUdFOiA4MCxcbiAgQ1JJVElDQUxfVEhSRVNIT0xEX1BFUkNFTlRBR0U6IDkwLFxuICBFWENFRURFRF9USFJFU0hPTERfUEVSQ0VOVEFHRTogMTAwXG59IGFzIGNvbnN0O1xuXG4vKipcbiAqIEzDrW1pdGVzIGRlIHJhdGUgbGltaXRpbmdcbiAqL1xuZXhwb3J0IGNvbnN0IFJBVEVfTElNSVRTID0ge1xuICBERUZBVUxUX1dJTkRPV19NSU5VVEVTOiA2MCxcbiAgREVGQVVMVF9NQVhfUkVRVUVTVFM6IDEwMCxcbiAgQVBJX1JFUVVFU1RTX1BFUl9NSU5VVEU6IDYwLFxuICBVUExPQURfUkVRVUVTVFNfUEVSX0hPVVI6IDEwLFxuICBBVVRIX0FUVEVNUFRTX1BFUl9IT1VSOiA1XG59IGFzIGNvbnN0O1xuXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4vLyBDT05TVEFOVEVTIERFIFRJRU1QTyBZIENPTkZJR1VSQUNJw5NOXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG5cbi8qKlxuICogVGltZW91dHMgeSBpbnRlcnZhbG9zXG4gKi9cbmV4cG9ydCBjb25zdCBUSU1FT1VUUyA9IHtcbiAgU0VTU0lPTl9USU1FT1VUX01TOiA1ICogNjAgKiAxMDAwLCAvLyA1IG1pbnV0b3NcbiAgQVBJX1RJTUVPVVRfTVM6IDMwICogMTAwMCwgLy8gMzAgc2VndW5kb3NcbiAgVVBMT0FEX1RJTUVPVVRfTVM6IDYwICogMTAwMCwgLy8gMSBtaW51dG9cbiAgUkVUUllfREVMQVlfTVM6IDEwMDAsIC8vIDEgc2VndW5kb1xuICBQT0xMSU5HX0lOVEVSVkFMX01TOiAyMDAwIC8vIDIgc2VndW5kb3Ncbn0gYXMgY29uc3Q7XG5cbi8qKlxuICogQ29uZmlndXJhY2nDs24gZGUgcmVpbnRlbnRvc1xuICovXG5leHBvcnQgY29uc3QgUkVUUllfQ09ORklHID0ge1xuICBNQVhfQVRURU1QVFM6IDMsXG4gIEJBQ0tPRkZfTVVMVElQTElFUjogMixcbiAgSU5JVElBTF9ERUxBWV9NUzogMTAwMFxufSBhcyBjb25zdDtcblxuLyoqXG4gKiBDb25maWd1cmFjacOzbiBkZSBzZWd1cmlkYWRcbiAqL1xuZXhwb3J0IGNvbnN0IFNFQ1VSSVRZX0NPTkZJRyA9IHtcbiAgRU5BQkxFX1NUUklDVF9WQUxJREFUSU9OOiBwcm9jZXNzLmVudi5TVFJJQ1RfUExBTl9WQUxJREFUSU9OID09PSAndHJ1ZScsXG4gIFJFUVVJUkVfUEFZTUVOVF9WRVJJRklDQVRJT046IHByb2Nlc3MuZW52LlJFUVVJUkVfUEFZTUVOVF9WRVJJRklDQVRJT04gPT09ICd0cnVlJyxcbiAgRU5BQkxFX0FDQ0VTU19MT0dHSU5HOiBwcm9jZXNzLmVudi5FTkFCTEVfQUNDRVNTX0xPR0dJTkcgPT09ICd0cnVlJyxcbiAgRU5BQkxFX0ZFQVRVUkVfVkFMSURBVElPTjogcHJvY2Vzcy5lbnYuRU5BQkxFX0ZFQVRVUkVfVkFMSURBVElPTiA9PT0gJ3RydWUnLFxuICBBVVRPX0FDVElWQVRFX1BBWU1FTlRTOiBwcm9jZXNzLmVudi5BVVRPX0FDVElWQVRFX1BBWU1FTlRTID09PSAndHJ1ZScsXG4gIEVOQUJMRV9QVUJMSUNfU0lHTlVQOiBwcm9jZXNzLmVudi5FTkFCTEVfUFVCTElDX1NJR05VUCA9PT0gJ3RydWUnXG59IGFzIGNvbnN0O1xuXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4vLyBDT05TVEFOVEVTIERFIE1FTlNBSkVTIFkgVEVYVE9TXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG5cbi8qKlxuICogTWVuc2FqZXMgZGUgZXJyb3IgY29tdW5lc1xuICovXG5leHBvcnQgY29uc3QgRVJST1JfTUVTU0FHRVMgPSB7XG4gIFVOQVVUSE9SSVpFRDogJ05vIGF1dG9yaXphZG8nLFxuICBGT1JCSURERU46ICdBY2Nlc28gZGVuZWdhZG8nLFxuICBOT1RfRk9VTkQ6ICdSZWN1cnNvIG5vIGVuY29udHJhZG8nLFxuICBJTlRFUk5BTF9FUlJPUjogJ0Vycm9yIGludGVybm8gZGVsIHNlcnZpZG9yJyxcbiAgSU5WQUxJRF9EQVRBOiAnRGF0b3MgaW52w6FsaWRvcycsXG4gIFVTRVJfTk9UX0ZPVU5EOiAnVXN1YXJpbyBubyBlbmNvbnRyYWRvJyxcbiAgUFJPRklMRV9OT1RfRk9VTkQ6ICdQZXJmaWwgZGUgdXN1YXJpbyBubyBlbmNvbnRyYWRvJyxcbiAgUEFZTUVOVF9SRVFVSVJFRDogJ1BhZ28gcmVxdWVyaWRvJyxcbiAgTElNSVRfRVhDRUVERUQ6ICdMw61taXRlIGV4Y2VkaWRvJyxcbiAgRklMRV9UT09fTEFSR0U6ICdFbCBhcmNoaXZvIGVzIGRlbWFzaWFkbyBncmFuZGUnLFxuICBJTlZBTElEX0ZJTEVfVFlQRTogJ1RpcG8gZGUgYXJjaGl2byBubyB2w6FsaWRvJyxcbiAgVVBMT0FEX0ZBSUxFRDogJ0Vycm9yIGFsIHN1YmlyIGVsIGFyY2hpdm8nLFxuICBQUk9DRVNTSU5HX0VSUk9SOiAnRXJyb3IgYWwgcHJvY2VzYXIgbGEgc29saWNpdHVkJyxcbiAgTkVUV09SS19FUlJPUjogJ0Vycm9yIGRlIGNvbmV4acOzbicsXG4gIFRJTUVPVVRfRVJST1I6ICdUaWVtcG8gZGUgZXNwZXJhIGFnb3RhZG8nXG59IGFzIGNvbnN0O1xuXG4vKipcbiAqIE1lbnNhamVzIGRlIMOpeGl0b1xuICovXG5leHBvcnQgY29uc3QgU1VDQ0VTU19NRVNTQUdFUyA9IHtcbiAgVVBMT0FEX1NVQ0NFU1M6ICdBcmNoaXZvIHN1YmlkbyBjb3JyZWN0YW1lbnRlJyxcbiAgU0FWRV9TVUNDRVNTOiAnR3VhcmRhZG8gY29ycmVjdGFtZW50ZScsXG4gIFVQREFURV9TVUNDRVNTOiAnQWN0dWFsaXphZG8gY29ycmVjdGFtZW50ZScsXG4gIERFTEVURV9TVUNDRVNTOiAnRWxpbWluYWRvIGNvcnJlY3RhbWVudGUnLFxuICBQQVlNRU5UX1NVQ0NFU1M6ICdQYWdvIHByb2Nlc2FkbyBjb3JyZWN0YW1lbnRlJyxcbiAgUkVHSVNUUkFUSU9OX1NVQ0NFU1M6ICdSZWdpc3RybyBjb21wbGV0YWRvJyxcbiAgTE9HSU5fU1VDQ0VTUzogJ1Nlc2nDs24gaW5pY2lhZGEnLFxuICBMT0dPVVRfU1VDQ0VTUzogJ1Nlc2nDs24gY2VycmFkYScsXG4gIFBBU1NXT1JEX1JFU0VUX1NVQ0NFU1M6ICdDb250cmFzZcOxYSByZXN0YWJsZWNpZGEnLFxuICBFTUFJTF9TRU5UOiAnRW1haWwgZW52aWFkbyBjb3JyZWN0YW1lbnRlJ1xufSBhcyBjb25zdDtcblxuLyoqXG4gKiBNZW5zYWplcyBkZSB2YWxpZGFjacOzblxuICovXG5leHBvcnQgY29uc3QgVkFMSURBVElPTl9NRVNTQUdFUyA9IHtcbiAgUkVRVUlSRURfRklFTEQ6ICdFc3RlIGNhbXBvIGVzIG9ibGlnYXRvcmlvJyxcbiAgSU5WQUxJRF9FTUFJTDogJ0VtYWlsIG5vIHbDoWxpZG8nLFxuICBQQVNTV09SRF9UT09fU0hPUlQ6IGBMYSBjb250cmFzZcOxYSBkZWJlIHRlbmVyIGFsIG1lbm9zICR7VEVYVF9MSU1JVFMuTUlOX1BBU1NXT1JEX0xFTkdUSH0gY2FyYWN0ZXJlc2AsXG4gIFBBU1NXT1JEX1RPT19MT05HOiBgTGEgY29udHJhc2XDsWEgbm8gcHVlZGUgdGVuZXIgbcOhcyBkZSAke1RFWFRfTElNSVRTLk1BWF9QQVNTV09SRF9MRU5HVEh9IGNhcmFjdGVyZXNgLFxuICBQQVNTV09SRFNfRE9OVF9NQVRDSDogJ0xhcyBjb250cmFzZcOxYXMgbm8gY29pbmNpZGVuJyxcbiAgSU5WQUxJRF9GSUxFX1NJWkU6IGBFbCBhcmNoaXZvIG5vIHB1ZWRlIHN1cGVyYXIgJHtGSUxFX0xJTUlUUy5NQVhfU0laRV9NQn1NQmAsXG4gIElOVkFMSURfRklMRV9GT1JNQVQ6ICdGb3JtYXRvIGRlIGFyY2hpdm8gbm8gdsOhbGlkbycsXG4gIFRFWFRfVE9PX1NIT1JUOiBgRWwgdGV4dG8gZGViZSB0ZW5lciBhbCBtZW5vcyAke1RFWFRfTElNSVRTLk1JTl9DT05URU5UX0xFTkdUSH0gY2FyYWN0ZXJlc2AsXG4gIFRFWFRfVE9PX0xPTkc6IGBFbCB0ZXh0byBubyBwdWVkZSBzdXBlcmFyICR7VEVYVF9MSU1JVFMuTUFYX0NPTlRFTlRfTEVOR1RIfSBjYXJhY3RlcmVzYFxufSBhcyBjb25zdDtcblxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuLy8gQ09OU1RBTlRFUyBERSBFU1RBRE8gWSBUSVBPU1xuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuXG4vKipcbiAqIEVzdGFkb3MgZGUgcHJvY2VzYW1pZW50b1xuICovXG5leHBvcnQgY29uc3QgUFJPQ0VTU0lOR19TVEFURVMgPSB7XG4gIElETEU6ICdpZGxlJyxcbiAgTE9BRElORzogJ2xvYWRpbmcnLFxuICBQUk9DRVNTSU5HOiAncHJvY2Vzc2luZycsXG4gIFNVQ0NFU1M6ICdzdWNjZXNzJyxcbiAgRVJST1I6ICdlcnJvcicsXG4gIENBTkNFTExFRDogJ2NhbmNlbGxlZCdcbn0gYXMgY29uc3Q7XG5cbi8qKlxuICogRXN0YWRvcyBkZSBwYWdvXG4gKi9cbmV4cG9ydCBjb25zdCBQQVlNRU5UX1NUQVRFUyA9IHtcbiAgUEVORElORzogJ3BlbmRpbmcnLFxuICBQUk9DRVNTSU5HOiAncHJvY2Vzc2luZycsXG4gIENPTVBMRVRFRDogJ2NvbXBsZXRlZCcsXG4gIEZBSUxFRDogJ2ZhaWxlZCcsXG4gIENBTkNFTExFRDogJ2NhbmNlbGxlZCcsXG4gIFJFRlVOREVEOiAncmVmdW5kZWQnXG59IGFzIGNvbnN0O1xuXG4vKipcbiAqIFRpcG9zIGRlIG5vdGlmaWNhY2nDs25cbiAqL1xuZXhwb3J0IGNvbnN0IE5PVElGSUNBVElPTl9UWVBFUyA9IHtcbiAgSU5GTzogJ2luZm8nLFxuICBTVUNDRVNTOiAnc3VjY2VzcycsXG4gIFdBUk5JTkc6ICd3YXJuaW5nJyxcbiAgRVJST1I6ICdlcnJvcidcbn0gYXMgY29uc3Q7XG5cbi8qKlxuICogTml2ZWxlcyBkZSBzZXZlcmlkYWRcbiAqL1xuZXhwb3J0IGNvbnN0IFNFVkVSSVRZX0xFVkVMUyA9IHtcbiAgTE9XOiAnbG93JyxcbiAgTUVESVVNOiAnbWVkaXVtJyxcbiAgSElHSDogJ2hpZ2gnLFxuICBDUklUSUNBTDogJ2NyaXRpY2FsJ1xufSBhcyBjb25zdDtcblxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuLy8gQ09OU1RBTlRFUyBERSBDT05GSUdVUkFDScOTTiBERSBFTlRPUk5PXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG5cbi8qKlxuICogVmFyaWFibGVzIGRlIGVudG9ybm8gcmVxdWVyaWRhc1xuICovXG5leHBvcnQgY29uc3QgUkVRVUlSRURfRU5WX1ZBUlMgPSBbXG4gICdORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwnLFxuICAnTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVknLFxuICAnU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWScsXG4gICdTVFJJUEVfU0VDUkVUX0tFWScsXG4gICdTVFJJUEVfV0VCSE9PS19TRUNSRVQnLFxuICAnTkVYVF9QVUJMSUNfU1RSSVBFX1BVQkxJU0hBQkxFX0tFWSdcbl0gYXMgY29uc3Q7XG5cbi8qKlxuICogQ29uZmlndXJhY2nDs24gZGUgYXV0b21hdGl6YWNpw7NuXG4gKi9cbmV4cG9ydCBjb25zdCBBVVRPTUFUSU9OX0NPTkZJRyA9IHtcbiAgSU5WSVRBVElPTl9FWFBJUllfSE9VUlM6IHBhcnNlSW50KHByb2Nlc3MuZW52LklOVklUQVRJT05fRVhQSVJZX0hPVVJTIHx8ICcyNCcpLFxuICBERUZBVUxUX1RSSUFMX0RBWVM6IDUsXG4gIERFRkFVTFRfR1JBQ0VfUEVSSU9EX0RBWVM6IDNcbn0gYXMgY29uc3Q7XG5cbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbi8vIENPTlNUQU5URVMgREUgQlVTSU5FU1MgTE9HSUNcbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cblxuLyoqXG4gKiBDb3N0b3MgeSBwcmVjaW9zIChlbiBjZW50YXZvcylcbiAqL1xuZXhwb3J0IGNvbnN0IFBSSUNJTkcgPSB7XG4gIEFERElUSU9OQUxfVE9LRU5TX1BSSUNFOiAxMDAwLCAvLyDigqwxMC4wMCBwb3IgMU0gdG9rZW5zXG4gIEFERElUSU9OQUxfVE9LRU5TX0FNT1VOVDogMTAwMDAwMCxcbiAgRlJFRV9QTEFOX1BSSUNFOiAwLFxuICBCQVNJQ19QTEFOX1BSSUNFOiA5OTksIC8vIOKCrDkuOTlcbiAgUFJPX1BMQU5fUFJJQ0U6IDE5OTkgLy8g4oKsMTkuOTlcbn0gYXMgY29uc3Q7XG5cbi8qKlxuICogTMOtbWl0ZXMgZGUgcGxhbmVzIGdyYXR1aXRvc1xuICovXG5leHBvcnQgY29uc3QgRlJFRV9QTEFOX0xJTUlUUyA9IHtcbiAgRE9DVU1FTlRTOiAxLFxuICBNSU5EX01BUFNfVFJJQUw6IDIsXG4gIFRFU1RTX1RSSUFMOiAxMCxcbiAgRkxBU0hDQVJEU19UUklBTDogMTAsXG4gIFRPS0VOU19UUklBTDogNTAwMDAsXG4gIFRSSUFMX0RBWVM6IDVcbn0gYXMgY29uc3Q7XG5cbi8qKlxuICogRmFjdG9yZXMgZGUgcmllc2dvIGRlIHNlZ3VyaWRhZFxuICovXG5leHBvcnQgY29uc3QgU0VDVVJJVFlfUklTS19TQ09SRVMgPSB7XG4gIE1JU1NJTkdfVVNFUl9BR0VOVDogMzAsXG4gIEJPVF9VU0VSX0FHRU5UOiAyMCxcbiAgRVhURVJOQUxfUkVGRVJFUjogMTAsXG4gIFNVU1BJQ0lPVVNfUEFUVEVSTjogMjUsXG4gIEhJR0hfRlJFUVVFTkNZOiA0MFxufSBhcyBjb25zdDtcbiJdLCJuYW1lcyI6WyJBUFBfVVJMUyIsIkJBU0UiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQVBQX1VSTCIsIlNJVEUiLCJORVhUX1BVQkxJQ19TSVRFX1VSTCIsIlVQR1JBREVfUExBTiIsIlRIQU5LX1lPVSIsIkxPR0lOIiwiREFTSEJPQVJEIiwiUFJPRklMRSIsIldFTENPTUUiLCJQVUJMSUNfUk9VVEVTIiwiQVVUSEVOVElDQVRFRF9ST1VURVMiLCJGSUxFX0xJTUlUUyIsIk1BWF9TSVpFX01CIiwiTUFYX1NJWkVfQllURVMiLCJBTExPV0VEX1RZUEVTIiwiQUxMT1dFRF9FWFRFTlNJT05TIiwiVEVYVF9MSU1JVFMiLCJNSU5fUEFTU1dPUkRfTEVOR1RIIiwiTUFYX1BBU1NXT1JEX0xFTkdUSCIsIk1BWF9ET0NVTUVOVF9USVRMRV9MRU5HVEgiLCJNQVhfREVTQ1JJUFRJT05fTEVOR1RIIiwiTUlOX0NPTlRFTlRfTEVOR1RIIiwiTUFYX0NPTlRFTlRfTEVOR1RIIiwiVE9LRU5fTElNSVRTIiwiREVGQVVMVF9GUkVFX0xJTUlUIiwiV0FSTklOR19USFJFU0hPTERfUEVSQ0VOVEFHRSIsIkNSSVRJQ0FMX1RIUkVTSE9MRF9QRVJDRU5UQUdFIiwiRVhDRUVERURfVEhSRVNIT0xEX1BFUkNFTlRBR0UiLCJSQVRFX0xJTUlUUyIsIkRFRkFVTFRfV0lORE9XX01JTlVURVMiLCJERUZBVUxUX01BWF9SRVFVRVNUUyIsIkFQSV9SRVFVRVNUU19QRVJfTUlOVVRFIiwiVVBMT0FEX1JFUVVFU1RTX1BFUl9IT1VSIiwiQVVUSF9BVFRFTVBUU19QRVJfSE9VUiIsIlRJTUVPVVRTIiwiU0VTU0lPTl9USU1FT1VUX01TIiwiQVBJX1RJTUVPVVRfTVMiLCJVUExPQURfVElNRU9VVF9NUyIsIlJFVFJZX0RFTEFZX01TIiwiUE9MTElOR19JTlRFUlZBTF9NUyIsIlJFVFJZX0NPTkZJRyIsIk1BWF9BVFRFTVBUUyIsIkJBQ0tPRkZfTVVMVElQTElFUiIsIklOSVRJQUxfREVMQVlfTVMiLCJTRUNVUklUWV9DT05GSUciLCJFTkFCTEVfU1RSSUNUX1ZBTElEQVRJT04iLCJTVFJJQ1RfUExBTl9WQUxJREFUSU9OIiwiUkVRVUlSRV9QQVlNRU5UX1ZFUklGSUNBVElPTiIsIkVOQUJMRV9BQ0NFU1NfTE9HR0lORyIsIkVOQUJMRV9GRUFUVVJFX1ZBTElEQVRJT04iLCJBVVRPX0FDVElWQVRFX1BBWU1FTlRTIiwiRU5BQkxFX1BVQkxJQ19TSUdOVVAiLCJFUlJPUl9NRVNTQUdFUyIsIlVOQVVUSE9SSVpFRCIsIkZPUkJJRERFTiIsIk5PVF9GT1VORCIsIklOVEVSTkFMX0VSUk9SIiwiSU5WQUxJRF9EQVRBIiwiVVNFUl9OT1RfRk9VTkQiLCJQUk9GSUxFX05PVF9GT1VORCIsIlBBWU1FTlRfUkVRVUlSRUQiLCJMSU1JVF9FWENFRURFRCIsIkZJTEVfVE9PX0xBUkdFIiwiSU5WQUxJRF9GSUxFX1RZUEUiLCJVUExPQURfRkFJTEVEIiwiUFJPQ0VTU0lOR19FUlJPUiIsIk5FVFdPUktfRVJST1IiLCJUSU1FT1VUX0VSUk9SIiwiU1VDQ0VTU19NRVNTQUdFUyIsIlVQTE9BRF9TVUNDRVNTIiwiU0FWRV9TVUNDRVNTIiwiVVBEQVRFX1NVQ0NFU1MiLCJERUxFVEVfU1VDQ0VTUyIsIlBBWU1FTlRfU1VDQ0VTUyIsIlJFR0lTVFJBVElPTl9TVUNDRVNTIiwiTE9HSU5fU1VDQ0VTUyIsIkxPR09VVF9TVUNDRVNTIiwiUEFTU1dPUkRfUkVTRVRfU1VDQ0VTUyIsIkVNQUlMX1NFTlQiLCJWQUxJREFUSU9OX01FU1NBR0VTIiwiUkVRVUlSRURfRklFTEQiLCJJTlZBTElEX0VNQUlMIiwiUEFTU1dPUkRfVE9PX1NIT1JUIiwiUEFTU1dPUkRfVE9PX0xPTkciLCJQQVNTV09SRFNfRE9OVF9NQVRDSCIsIklOVkFMSURfRklMRV9TSVpFIiwiSU5WQUxJRF9GSUxFX0ZPUk1BVCIsIlRFWFRfVE9PX1NIT1JUIiwiVEVYVF9UT09fTE9ORyIsIlBST0NFU1NJTkdfU1RBVEVTIiwiSURMRSIsIkxPQURJTkciLCJQUk9DRVNTSU5HIiwiU1VDQ0VTUyIsIkVSUk9SIiwiQ0FOQ0VMTEVEIiwiUEFZTUVOVF9TVEFURVMiLCJQRU5ESU5HIiwiQ09NUExFVEVEIiwiRkFJTEVEIiwiUkVGVU5ERUQiLCJOT1RJRklDQVRJT05fVFlQRVMiLCJJTkZPIiwiV0FSTklORyIsIlNFVkVSSVRZX0xFVkVMUyIsIkxPVyIsIk1FRElVTSIsIkhJR0giLCJDUklUSUNBTCIsIlJFUVVJUkVEX0VOVl9WQVJTIiwiQVVUT01BVElPTl9DT05GSUciLCJJTlZJVEFUSU9OX0VYUElSWV9IT1VSUyIsInBhcnNlSW50IiwiREVGQVVMVF9UUklBTF9EQVlTIiwiREVGQVVMVF9HUkFDRV9QRVJJT0RfREFZUyIsIlBSSUNJTkciLCJBRERJVElPTkFMX1RPS0VOU19QUklDRSIsIkFERElUSU9OQUxfVE9LRU5TX0FNT1VOVCIsIkZSRUVfUExBTl9QUklDRSIsIkJBU0lDX1BMQU5fUFJJQ0UiLCJQUk9fUExBTl9QUklDRSIsIkZSRUVfUExBTl9MSU1JVFMiLCJET0NVTUVOVFMiLCJNSU5EX01BUFNfVFJJQUwiLCJURVNUU19UUklBTCIsIkZMQVNIQ0FSRFNfVFJJQUwiLCJUT0tFTlNfVFJJQUwiLCJUUklBTF9EQVlTIiwiU0VDVVJJVFlfUklTS19TQ09SRVMiLCJNSVNTSU5HX1VTRVJfQUdFTlQiLCJCT1RfVVNFUl9BR0VOVCIsIkVYVEVSTkFMX1JFRkVSRVIiLCJTVVNQSUNJT1VTX1BBVFRFUk4iLCJISUdIX0ZSRVFVRU5DWSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/config/constants.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/config/features.ts":
/*!********************************!*\
  !*** ./src/config/features.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACTION_TO_FEATURE_MAP: () => (/* binding */ ACTION_TO_FEATURE_MAP),\n/* harmony export */   ACTION_TYPES: () => (/* binding */ ACTION_TYPES),\n/* harmony export */   ACTIVITY_TO_FEATURE_MAP: () => (/* binding */ ACTIVITY_TO_FEATURE_MAP),\n/* harmony export */   FEATURES_CONFIG: () => (/* binding */ FEATURES_CONFIG),\n/* harmony export */   FEATURE_IDS: () => (/* binding */ FEATURE_IDS),\n/* harmony export */   PLAN_RESTRICTED_ROUTES: () => (/* binding */ PLAN_RESTRICTED_ROUTES),\n/* harmony export */   actionToFeature: () => (/* binding */ actionToFeature),\n/* harmony export */   activityToFeature: () => (/* binding */ activityToFeature),\n/* harmony export */   featureRequiresPayment: () => (/* binding */ featureRequiresPayment),\n/* harmony export */   getAllFeatureIds: () => (/* binding */ getAllFeatureIds),\n/* harmony export */   getAllFeatures: () => (/* binding */ getAllFeatures),\n/* harmony export */   getFeatureConfig: () => (/* binding */ getFeatureConfig),\n/* harmony export */   getFeatureDisplayName: () => (/* binding */ getFeatureDisplayName),\n/* harmony export */   getFeatureTokensRequired: () => (/* binding */ getFeatureTokensRequired),\n/* harmony export */   getFeaturesByCategory: () => (/* binding */ getFeaturesByCategory),\n/* harmony export */   getFeaturesForPlan: () => (/* binding */ getFeaturesForPlan),\n/* harmony export */   isValidFeatureId: () => (/* binding */ isValidFeatureId)\n/* harmony export */ });\n// src/config/features.ts\n// Configuración centralizada de características y funcionalidades\n// ============================================================================\n// CONSTANTES DE FEATURES\n// ============================================================================\n/**\n * Identificadores únicos de características del sistema\n */ const FEATURE_IDS = {\n    DOCUMENT_UPLOAD: 'document_upload',\n    TEST_GENERATION: 'test_generation',\n    FLASHCARD_GENERATION: 'flashcard_generation',\n    MIND_MAP_GENERATION: 'mind_map_generation',\n    AI_TUTOR_CHAT: 'ai_tutor_chat',\n    STUDY_PLANNING: 'study_planning',\n    SUMMARY_A1_A2: 'summary_a1_a2'\n};\n/**\n * Acciones que pueden realizarse en el sistema\n */ const ACTION_TYPES = {\n    TEST_GENERATION: 'test_generation',\n    FLASHCARD_GENERATION: 'flashcard_generation',\n    MIND_MAP_GENERATION: 'mind_map_generation',\n    AI_CHAT: 'ai_chat',\n    STUDY_PLANNING: 'study_planning',\n    SUMMARY_GENERATION: 'summary_generation'\n};\n/**\n * Configuración de todas las características del sistema\n */ const FEATURES_CONFIG = {\n    [FEATURE_IDS.DOCUMENT_UPLOAD]: {\n        id: FEATURE_IDS.DOCUMENT_UPLOAD,\n        name: 'document_upload',\n        displayName: 'Subida de documentos',\n        description: 'Permite subir y procesar documentos PDF para estudio',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 0,\n        icon: 'FiUpload',\n        route: '/app'\n    },\n    [FEATURE_IDS.TEST_GENERATION]: {\n        id: FEATURE_IDS.TEST_GENERATION,\n        name: 'test_generation',\n        displayName: 'Generación de tests',\n        description: 'Genera tests automáticos basados en el contenido de estudio',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 5000,\n        icon: 'FiFileText',\n        route: '/app/tests'\n    },\n    [FEATURE_IDS.FLASHCARD_GENERATION]: {\n        id: FEATURE_IDS.FLASHCARD_GENERATION,\n        name: 'flashcard_generation',\n        displayName: 'Generación de flashcards',\n        description: 'Crea flashcards inteligentes para memorización efectiva',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 3000,\n        icon: 'FiLayers',\n        route: '/app/flashcards'\n    },\n    [FEATURE_IDS.MIND_MAP_GENERATION]: {\n        id: FEATURE_IDS.MIND_MAP_GENERATION,\n        name: 'mind_map_generation',\n        displayName: 'Generación de mapas mentales',\n        description: 'Genera mapas mentales visuales para mejor comprensión',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 4000,\n        icon: 'FiGitBranch',\n        route: '/app/mindmaps'\n    },\n    [FEATURE_IDS.AI_TUTOR_CHAT]: {\n        id: FEATURE_IDS.AI_TUTOR_CHAT,\n        name: 'ai_tutor_chat',\n        displayName: 'Chat con preparador IA',\n        description: 'Interactúa con un preparador de oposiciones inteligente',\n        category: 'premium',\n        minimumPlans: [\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: true,\n        tokensRequired: 2000,\n        icon: 'FiMessageSquare',\n        route: '/app/ai-tutor'\n    },\n    [FEATURE_IDS.STUDY_PLANNING]: {\n        id: FEATURE_IDS.STUDY_PLANNING,\n        name: 'study_planning',\n        displayName: 'Planificación de estudios',\n        description: 'Crea planes de estudio personalizados y estructurados',\n        category: 'advanced',\n        minimumPlans: [\n            'pro'\n        ],\n        requiresPayment: true,\n        tokensRequired: 20000,\n        icon: 'FiCalendar',\n        route: '/plan-estudios'\n    },\n    [FEATURE_IDS.SUMMARY_A1_A2]: {\n        id: FEATURE_IDS.SUMMARY_A1_A2,\n        name: 'summary_a1_a2',\n        displayName: 'Resúmenes A1 y A2',\n        description: 'Genera resúmenes especializados para oposiciones A1 y A2',\n        category: 'advanced',\n        minimumPlans: [\n            'pro'\n        ],\n        requiresPayment: true,\n        tokensRequired: 6000,\n        icon: 'FiBook',\n        route: '/app/summaries'\n    }\n};\n// ============================================================================\n// MAPEOS Y UTILIDADES\n// ============================================================================\n/**\n * Mapeo de acciones a características\n */ const ACTION_TO_FEATURE_MAP = {\n    [ACTION_TYPES.TEST_GENERATION]: FEATURE_IDS.TEST_GENERATION,\n    [ACTION_TYPES.FLASHCARD_GENERATION]: FEATURE_IDS.FLASHCARD_GENERATION,\n    [ACTION_TYPES.MIND_MAP_GENERATION]: FEATURE_IDS.MIND_MAP_GENERATION,\n    [ACTION_TYPES.AI_CHAT]: FEATURE_IDS.AI_TUTOR_CHAT,\n    [ACTION_TYPES.STUDY_PLANNING]: FEATURE_IDS.STUDY_PLANNING,\n    [ACTION_TYPES.SUMMARY_GENERATION]: FEATURE_IDS.SUMMARY_A1_A2\n};\n/**\n * Mapeo de actividades de tokens a características\n */ const ACTIVITY_TO_FEATURE_MAP = {\n    'test_generation': FEATURE_IDS.TEST_GENERATION,\n    'flashcard_generation': FEATURE_IDS.FLASHCARD_GENERATION,\n    'mind_map_generation': FEATURE_IDS.MIND_MAP_GENERATION,\n    'ai_chat': FEATURE_IDS.AI_TUTOR_CHAT,\n    'study_planning': FEATURE_IDS.STUDY_PLANNING,\n    'summary_generation': FEATURE_IDS.SUMMARY_A1_A2,\n    'document_analysis': FEATURE_IDS.DOCUMENT_UPLOAD\n};\n/**\n * Configuración de rutas restringidas por plan\n */ const PLAN_RESTRICTED_ROUTES = {\n    '/plan-estudios': [\n        'pro'\n    ],\n    '/app/ai-tutor': [\n        'usuario',\n        'pro'\n    ],\n    '/app/summaries': [\n        'pro'\n    ],\n    '/app/advanced-features': [\n        'pro'\n    ]\n};\n// ============================================================================\n// FUNCIONES UTILITARIAS\n// ============================================================================\n/**\n * Obtiene la configuración de una característica\n */ function getFeatureConfig(featureId) {\n    return FEATURES_CONFIG[featureId];\n}\n/**\n * Obtiene el nombre para mostrar de una característica\n */ function getFeatureDisplayName(featureId) {\n    const config = FEATURES_CONFIG[featureId];\n    return (config === null || config === void 0 ? void 0 : config.displayName) || featureId;\n}\n/**\n * Obtiene todas las características de una categoría\n */ function getFeaturesByCategory(category) {\n    return Object.values(FEATURES_CONFIG).filter((feature)=>feature.category === category);\n}\n/**\n * Obtiene las características disponibles para un plan\n */ function getFeaturesForPlan(planId) {\n    return Object.values(FEATURES_CONFIG).filter((feature)=>feature.minimumPlans.includes(planId));\n}\n/**\n * Verifica si una característica requiere pago\n */ function featureRequiresPayment(featureId) {\n    const config = getFeatureConfig(featureId);\n    return (config === null || config === void 0 ? void 0 : config.requiresPayment) || false;\n}\n/**\n * Obtiene los tokens requeridos para una característica\n */ function getFeatureTokensRequired(featureId) {\n    const config = getFeatureConfig(featureId);\n    return (config === null || config === void 0 ? void 0 : config.tokensRequired) || 0;\n}\n/**\n * Convierte una acción a su característica correspondiente\n */ function actionToFeature(action) {\n    return ACTION_TO_FEATURE_MAP[action];\n}\n/**\n * Convierte una actividad a su característica correspondiente\n */ function activityToFeature(activity) {\n    return ACTIVITY_TO_FEATURE_MAP[activity];\n}\n/**\n * Obtiene todas las características como array\n */ function getAllFeatures() {\n    return Object.values(FEATURES_CONFIG);\n}\n/**\n * Obtiene los IDs de todas las características\n */ function getAllFeatureIds() {\n    return Object.keys(FEATURES_CONFIG);\n}\n/**\n * Verifica si un ID de característica es válido\n */ function isValidFeatureId(featureId) {\n    return featureId in FEATURES_CONFIG;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/config/features.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/config/index.ts":
/*!*****************************!*\
  !*** ./src/config/index.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACTION_TO_FEATURE_MAP: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.ACTION_TO_FEATURE_MAP),\n/* harmony export */   ACTION_TYPES: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.ACTION_TYPES),\n/* harmony export */   ACTIVITY_TO_FEATURE_MAP: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.ACTIVITY_TO_FEATURE_MAP),\n/* harmony export */   APP_URLS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.APP_URLS),\n/* harmony export */   AUTHENTICATED_ROUTES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.AUTHENTICATED_ROUTES),\n/* harmony export */   AUTOMATION_CONFIG: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.AUTOMATION_CONFIG),\n/* harmony export */   ERROR_MESSAGES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.ERROR_MESSAGES),\n/* harmony export */   FEATURES_CONFIG: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.FEATURES_CONFIG),\n/* harmony export */   FEATURE_IDS: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.FEATURE_IDS),\n/* harmony export */   FILE_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.FILE_LIMITS),\n/* harmony export */   FREE_PLAN_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.FREE_PLAN_LIMITS),\n/* harmony export */   LIMITS: () => (/* binding */ LIMITS),\n/* harmony export */   MESSAGES: () => (/* binding */ MESSAGES),\n/* harmony export */   NOTIFICATION_TYPES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.NOTIFICATION_TYPES),\n/* harmony export */   PAYMENT_STATES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.PAYMENT_STATES),\n/* harmony export */   PLAN_CONFIGURATIONS: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS),\n/* harmony export */   PLAN_RESTRICTED_ROUTES: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.PLAN_RESTRICTED_ROUTES),\n/* harmony export */   PRICING: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.PRICING),\n/* harmony export */   PROCESSING_STATES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.PROCESSING_STATES),\n/* harmony export */   PUBLIC_ROUTES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.PUBLIC_ROUTES),\n/* harmony export */   RATE_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.RATE_LIMITS),\n/* harmony export */   REQUIRED_ENV_VARS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.REQUIRED_ENV_VARS),\n/* harmony export */   RETRY_CONFIG: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.RETRY_CONFIG),\n/* harmony export */   SECURITY: () => (/* binding */ SECURITY),\n/* harmony export */   SECURITY_CONFIG: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.SECURITY_CONFIG),\n/* harmony export */   SECURITY_RISK_SCORES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.SECURITY_RISK_SCORES),\n/* harmony export */   SUCCESS_MESSAGES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.SUCCESS_MESSAGES),\n/* harmony export */   TEXT_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.TEXT_LIMITS),\n/* harmony export */   TIMEOUTS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.TIMEOUTS),\n/* harmony export */   TIME_CONFIG: () => (/* binding */ TIME_CONFIG),\n/* harmony export */   TOKEN_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.TOKEN_LIMITS),\n/* harmony export */   VALIDATION_MESSAGES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_MESSAGES),\n/* harmony export */   actionToFeature: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.actionToFeature),\n/* harmony export */   activityToFeature: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.activityToFeature),\n/* harmony export */   canPerformAction: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.canPerformAction),\n/* harmony export */   checkUserFeatureAccess: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.checkUserFeatureAccess),\n/* harmony export */   featureRequiresPayment: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.featureRequiresPayment),\n/* harmony export */   getAllFeatureIds: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getAllFeatureIds),\n/* harmony export */   getAllFeatures: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getAllFeatures),\n/* harmony export */   getFeatureConfig: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeatureConfig),\n/* harmony export */   getFeatureDisplayName: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeatureDisplayName),\n/* harmony export */   getFeatureTokensRequired: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeatureTokensRequired),\n/* harmony export */   getFeaturesByCategory: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeaturesByCategory),\n/* harmony export */   getFeaturesForPlan: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeaturesForPlan),\n/* harmony export */   getPlanConfiguration: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.getPlanConfiguration),\n/* harmony export */   getTokenLimitForPlan: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.getTokenLimitForPlan),\n/* harmony export */   getTrialLimit: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.getTrialLimit),\n/* harmony export */   getWeeklyLimit: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.getWeeklyLimit),\n/* harmony export */   hasFeatureAccess: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.hasFeatureAccess),\n/* harmony export */   isUnlimited: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.isUnlimited),\n/* harmony export */   isValidFeatureId: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.isValidFeatureId)\n/* harmony export */ });\n/* harmony import */ var _plans__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./plans */ \"(app-pages-browser)/./src/config/plans.ts\");\n/* harmony import */ var _features__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./features */ \"(app-pages-browser)/./src/config/features.ts\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants */ \"(app-pages-browser)/./src/config/constants.ts\");\n/**\n * Configuración centralizada - Re-exports\n * \n * Este archivo centraliza todas las configuraciones del sistema,\n * proporcionando un punto único de importación para cualquier configuración.\n * \n * Uso:\n * import { PLAN_CONFIGURATIONS, FEATURES_CONFIG, ERROR_MESSAGES } from '@/config';\n */ // ============================================================================\n// CONFIGURACIONES DE PLANES\n// ============================================================================\n\n// ============================================================================\n// CONFIGURACIONES DE FEATURES\n// ============================================================================\n\n// ============================================================================\n// CONSTANTES DEL SISTEMA\n// ============================================================================\n\n// ============================================================================\n// RE-EXPORTS COMBINADOS PARA CONVENIENCIA\n// ============================================================================\n// Importar constantes para uso en re-exports combinados\n\n/**\n * Todas las configuraciones de límites en un solo objeto\n */ const LIMITS = {\n    FILE: _constants__WEBPACK_IMPORTED_MODULE_2__.FILE_LIMITS,\n    TEXT: _constants__WEBPACK_IMPORTED_MODULE_2__.TEXT_LIMITS,\n    TOKEN: _constants__WEBPACK_IMPORTED_MODULE_2__.TOKEN_LIMITS,\n    RATE: _constants__WEBPACK_IMPORTED_MODULE_2__.RATE_LIMITS\n};\n/**\n * Todas las configuraciones de tiempo en un solo objeto\n */ const TIME_CONFIG = {\n    TIMEOUTS: _constants__WEBPACK_IMPORTED_MODULE_2__.TIMEOUTS,\n    RETRY: _constants__WEBPACK_IMPORTED_MODULE_2__.RETRY_CONFIG\n};\n/**\n * Todos los mensajes del sistema en un solo objeto\n */ const MESSAGES = {\n    ERROR: _constants__WEBPACK_IMPORTED_MODULE_2__.ERROR_MESSAGES,\n    SUCCESS: _constants__WEBPACK_IMPORTED_MODULE_2__.SUCCESS_MESSAGES,\n    VALIDATION: _constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_MESSAGES\n};\n/**\n * Todas las configuraciones de seguridad en un solo objeto\n */ const SECURITY = {\n    CONFIG: _constants__WEBPACK_IMPORTED_MODULE_2__.SECURITY_CONFIG,\n    RISK_SCORES: _constants__WEBPACK_IMPORTED_MODULE_2__.SECURITY_RISK_SCORES\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/config/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/config/plans.ts":
/*!*****************************!*\
  !*** ./src/config/plans.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLAN_CONFIGURATIONS: () => (/* binding */ PLAN_CONFIGURATIONS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   checkUserFeatureAccess: () => (/* binding */ checkUserFeatureAccess),\n/* harmony export */   getPlanConfiguration: () => (/* binding */ getPlanConfiguration),\n/* harmony export */   getTokenLimitForPlan: () => (/* binding */ getTokenLimitForPlan),\n/* harmony export */   getTrialLimit: () => (/* binding */ getTrialLimit),\n/* harmony export */   getWeeklyLimit: () => (/* binding */ getWeeklyLimit),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess),\n/* harmony export */   isUnlimited: () => (/* binding */ isUnlimited)\n/* harmony export */ });\n// src/config/plans.ts\n// Configuración centralizada de límites y características por plan\n// Configuración completa de planes con límites y características\nconst PLAN_CONFIGURATIONS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        limits: {\n            documents: 1,\n            mindMapsForTrial: 2,\n            testsForTrial: 10,\n            flashcardsForTrial: 10,\n            tokensForTrial: 50000,\n            features: [\n                'document_upload',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'ai_tutor_chat',\n            'summary_a1_a2'\n        ]\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        limits: {\n            documents: -1,\n            mindMapsPerWeek: -1,\n            testsPerWeek: -1,\n            flashcardsPerWeek: -1,\n            monthlyTokens: 1000000,\n            features: [\n                'document_upload',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'summary_a1_a2'\n        ]\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        limits: {\n            documents: -1,\n            mindMapsPerWeek: -1,\n            testsPerWeek: -1,\n            flashcardsPerWeek: -1,\n            monthlyTokens: 1000000,\n            features: [\n                'document_upload',\n                'study_planning',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation',\n                'summary_a1_a2'\n            ]\n        },\n        features: [\n            'document_upload',\n            'study_planning',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation',\n            'summary_a1_a2'\n        ],\n        restrictedFeatures: []\n    }\n};\n// Funciones de utilidad\nfunction getPlanConfiguration(planId) {\n    return PLAN_CONFIGURATIONS[planId] || null;\n}\nfunction getTokenLimitForPlan(planId) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 50000;\n    // Para plan gratuito, usar tokensForTrial\n    if (planId === 'free') {\n        return config.limits.tokensForTrial || 50000;\n    }\n    // Para planes de pago, usar monthlyTokens\n    return config.limits.monthlyTokens || 1000000;\n}\nfunction hasFeatureAccess(planId, feature) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return false;\n    // Si la característica está en la lista de restringidas, no tiene acceso\n    if (config.restrictedFeatures.includes(feature)) {\n        return false;\n    }\n    // Si no está restringida, verificar si está en las características permitidas\n    return config.features.includes(feature);\n}\nfunction getWeeklyLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 0;\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsPerWeek || 0;\n        case 'tests':\n            return config.limits.testsPerWeek || 0;\n        case 'flashcards':\n            return config.limits.flashcardsPerWeek || 0;\n        default:\n            return 0;\n    }\n}\n// Nueva función para obtener límites de trial (para plan gratuito)\nfunction getTrialLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config || planId !== 'free') return -1; // -1 para ilimitado o no aplica\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsForTrial || 0;\n        case 'tests':\n            return config.limits.testsForTrial || 0;\n        case 'flashcards':\n            return config.limits.flashcardsForTrial || 0;\n        case 'tokens':\n            return config.limits.tokensForTrial || 0;\n        default:\n            return 0;\n    }\n}\nfunction isUnlimited(limit) {\n    return limit === -1;\n}\n// Validar si un usuario puede realizar una acción específica\nfunction canPerformAction(planId, feature, currentUsage, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) {\n        return {\n            allowed: false,\n            reason: 'Plan no válido'\n        };\n    }\n    // Verificar si tiene acceso a la característica\n    if (!hasFeatureAccess(planId, feature)) {\n        return {\n            allowed: false,\n            reason: \"Caracter\\xedstica \".concat(feature, \" no disponible en \").concat(config.name)\n        };\n    }\n    // Verificar límites semanales si aplica\n    if (limitType) {\n        const weeklyLimit = getWeeklyLimit(planId, limitType);\n        if (!isUnlimited(weeklyLimit) && currentUsage >= weeklyLimit) {\n            return {\n                allowed: false,\n                reason: \"L\\xedmite semanal de \".concat(limitType, \" alcanzado (\").concat(weeklyLimit, \")\")\n            };\n        }\n    }\n    return {\n        allowed: true\n    };\n}\n// Función para verificar acceso de usuario (para uso en frontend)\nasync function checkUserFeatureAccess(feature) {\n    try {\n        // Obtener el plan del usuario desde la API\n        const response = await fetch('/api/user/plan');\n        if (!response.ok) {\n            console.error('Error obteniendo plan del usuario');\n            // Si hay error, asumir plan gratuito\n            return hasFeatureAccess('free', feature);\n        }\n        const { plan } = await response.json();\n        const userPlan = plan || 'free';\n        // Usar la misma lógica que la función hasFeatureAccess\n        return hasFeatureAccess(userPlan, feature);\n    } catch (error) {\n        console.error('Error verificando acceso a característica:', error);\n        // En caso de error, asumir plan gratuito\n        return hasFeatureAccess('free', feature);\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/config/plans.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/stripe/plans.ts":
/*!*********************************!*\
  !*** ./src/lib/stripe/plans.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ADDITIONAL_PRODUCTS: () => (/* binding */ ADDITIONAL_PRODUCTS),\n/* harmony export */   APP_URLS: () => (/* binding */ APP_URLS),\n/* harmony export */   PLANS: () => (/* binding */ PLANS),\n/* harmony export */   getFullPlanConfig: () => (/* binding */ getFullPlanConfig),\n/* harmony export */   getPlanById: () => (/* binding */ getPlanById),\n/* harmony export */   isValidPlan: () => (/* binding */ isValidPlan)\n/* harmony export */ });\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config */ \"(app-pages-browser)/./src/config/index.ts\");\n// src/lib/stripe/plans.ts\n// Configuración de planes integrada con sistema de límites\n\nconst PLANS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        stripeProductId: null,\n        stripePriceId: null,\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma solo durante 5 días',\n            '• Subida de documentos: máximo 1 documento',\n            '• Generador de test: máximo 10 preguntas test',\n            '• Generador de flashcards: máximo 10 tarjetas flashcard',\n            '• Generador de mapas mentales: máximo 2 mapas mentales',\n            'No incluye:',\n            '• Planificación de estudios',\n            '• Habla con tu preparador IA',\n            '• Resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1'\n        ],\n        limits: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.free.limits,\n        planConfig: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.free\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        stripeProductId: 'prod_SR65BdKdek1OXd',\n        // IMPORTANTE: Este precio debe ser recurrente (suscripción mensual) en Stripe\n        // Si actualmente es un pago único, crear un nuevo precio recurrente en Stripe Dashboard\n        stripePriceId: 'price_1Rae5807kFn3sIXhRf3adX1n',\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',\n            '• Subida de documentos',\n            '• Habla con tu preparador IA *',\n            '• Generador de test *',\n            '• Generador de flashcards *',\n            '• Generador de mapas mentales *',\n            '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 1.000.000 de tokens, podrán ampliarse durante el mes mediante pago',\n            'No incluye:',\n            '• Planificación de estudios',\n            '• Resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1'\n        ],\n        limits: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.usuario.limits,\n        planConfig: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.usuario\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        stripeProductId: 'prod_SR66U2G7bVJqu3',\n        stripePriceId: 'price_1Rae3U07kFn3sIXhkvSuJco1',\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',\n            '• Subida de documentos',\n            '• Planificación de estudios mediante IA*',\n            '• Habla con tu preparador IA *',\n            '• Generador de test *',\n            '• Generador de flashcards *',\n            '• Generador de mapas mentales *',\n            '• Generación de resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1',\n            '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 1.000.000 de tokens, podrán ampliarse durante el mes mediante pago'\n        ],\n        limits: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.pro.limits,\n        planConfig: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.pro\n    }\n};\n// Función para obtener plan por ID\nfunction getPlanById(planId) {\n    return PLANS[planId] || null;\n}\n// Función para validar si un plan es válido\nfunction isValidPlan(planId) {\n    return planId in PLANS;\n}\n// Función para obtener configuración completa del plan\nfunction getFullPlanConfig(planId) {\n    const plan = getPlanById(planId);\n    return (plan === null || plan === void 0 ? void 0 : plan.planConfig) || null;\n}\n// Configuración de productos adicionales\nconst ADDITIONAL_PRODUCTS = {\n    tokens: {\n        id: 'tokens',\n        name: 'Tokens Adicionales',\n        description: '1,000,000 tokens adicionales para tu cuenta',\n        price: 1000,\n        tokenAmount: 1000000,\n        // Estos IDs se deben crear en Stripe Dashboard\n        stripeProductId: 'prod_tokens_additional',\n        stripePriceId: 'price_tokens_additional'\n    }\n};\n// URLs de la aplicación\nconst APP_URLS = {\n    success: \"\".concat(\"http://localhost:3001\", \"/thank-you\"),\n    cancel: \"\".concat(\"http://localhost:3001\", \"/upgrade-plan\"),\n    webhook: \"\".concat(\"http://localhost:3001\", \"/api/stripe/webhook\")\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/stripe/plans.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cpayment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);