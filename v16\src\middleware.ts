// ===== Archivo: src/middleware.ts (VERSIÓN FINAL CON VALIDACIÓN DE PERFIL ACTIVO) =====

import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

// Lista de rutas que no requieren un perfil de usuario activo para ser vistas.
// Se incluyen rutas de autenticación, pago y la página principal de la app.
const ALLOWED_ROUTES_WITHOUT_ACTIVE_PROFILE = [
  '/', // Página principal
  '/login',
  '/auth',
  '/payment',
  '/upgrade-plan',
  '/thank-you',
  '/profile', // El usuario debe poder acceder a su perfil para ver el estado y actualizar
];

export async function middleware(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  // Agregar headers de seguridad
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          request.cookies.set({ name, value, ...options })
          response = NextResponse.next({
            request: { headers: request.headers },
          })
          response.cookies.set({ name, value, ...options })
        },
        remove(name: string, options: CookieOptions) {
          request.cookies.set({ name, value: '', ...options })
          response = NextResponse.next({
            request: { headers: request.headers },
          })
          response.cookies.set({ name, value: '', ...options })
        },
      },
    }
  );

  // 1. OBTENER LA SESIÓN DEL USUARIO
  const { data: { user } } = await supabase.auth.getUser();

  // 2. LÓGICA DE REDIRECCIÓN
  if (user) {
    // Si el usuario está logueado, vamos a verificar el estado de su cuenta
    // Usamos el cliente admin para evitar problemas con RLS
    const supabaseAdmin = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          get(name: string) {
            return request.cookies.get(name)?.value
          },
          set(name: string, value: string, options: CookieOptions) {
            // No necesitamos setear cookies con el cliente admin
          },
          remove(name: string, options: CookieOptions) {
            // No necesitamos remover cookies con el cliente admin
          },
        },
      }
    );

    const { data: profile } = await supabaseAdmin
      .from('user_profiles')
      .select('payment_verified, plan_expires_at, subscription_plan')
      .eq('user_id', user.id)
      .single();

    let isAccountActive = false;
    let reason = 'inactive_profile';

    if (profile) {
      const isExpired = profile.plan_expires_at && new Date(profile.plan_expires_at) < new Date();
      const paymentOk = profile.subscription_plan === 'free' || profile.payment_verified;

      if (!isExpired && paymentOk) {
        isAccountActive = true;
      } else if (isExpired) {
        reason = 'account_expired';
      } else if (!paymentOk) {
        reason = 'payment_not_verified';
      }
    }

    // Si la cuenta NO está activa y el usuario intenta acceder a una ruta protegida...
    if (!isAccountActive && !ALLOWED_ROUTES_WITHOUT_ACTIVE_PROFILE.some(path => request.nextUrl.pathname.startsWith(path))) {
      // ...lo redirigimos a su perfil para que vea el problema y pueda solucionarlo.
      const url = request.nextUrl.clone();
      url.pathname = '/profile';
      url.searchParams.set('reason', reason);
      return NextResponse.redirect(url);
    }

  } else {
    // Si NO hay usuario y la ruta no es pública, redirigir a login
    if (!ALLOWED_ROUTES_WITHOUT_ACTIVE_PROFILE.some(path => request.nextUrl.pathname.startsWith(path))) {
      const url = request.nextUrl.clone();
      url.pathname = '/login';
      return NextResponse.redirect(url);
    }
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
