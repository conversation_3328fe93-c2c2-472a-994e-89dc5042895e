// ===== Archivo: src/middleware.ts (VERSIÓN FINAL CON VALIDACIÓN DE PERFIL ACTIVO) =====

import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

// Lista de rutas que no requieren un perfil de usuario activo para ser vistas.
// Se incluyen rutas de autenticación, pago y la página principal de la app.
const ALLOWED_ROUTES_WITHOUT_ACTIVE_PROFILE = [
  '/', // Página principal
  '/login',
  '/auth',
  '/payment',
  '/upgrade-plan',
  '/thank-you',
  '/profile', // El usuario debe poder acceder a su perfil para ver el estado y actualizar
];

export async function middleware(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  // Agregar headers de seguridad
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          request.cookies.set({ name, value, ...options })
          response = NextResponse.next({
            request: { headers: request.headers },
          })
          response.cookies.set({ name, value, ...options })
        },
        remove(name: string, options: CookieOptions) {
          request.cookies.set({ name, value: '', ...options })
          response = NextResponse.next({
            request: { headers: request.headers },
          })
          response.cookies.set({ name, value: '', ...options })
        },
      },
    }
  );

  // 1. OBTENER LA SESIÓN DEL USUARIO
  const { data: { user } } = await supabase.auth.getUser();

  // 2. LÓGICA DE REDIRECCIÓN
  if (user) {
    // Si el usuario está logueado, vamos a verificar el estado de su cuenta
    // Usamos el cliente admin para evitar problemas con RLS
    const supabaseAdmin = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          get(name: string) {
            return request.cookies.get(name)?.value
          },
          set(name: string, value: string, options: CookieOptions) {
            // No necesitamos setear cookies con el cliente admin
          },
          remove(name: string, options: CookieOptions) {
            // No necesitamos remover cookies con el cliente admin
          },
        },
      }
    );

    const { data: profile } = await supabaseAdmin
      .from('user_profiles')
      .select('payment_verified, plan_expires_at, subscription_plan')
      .eq('user_id', user.id)
      .single();

    let isAccountActive = false;
    let reason = 'inactive_profile';

    if (profile) {
      const isExpired = profile.plan_expires_at && new Date(profile.plan_expires_at) < new Date();
      const paymentOk = profile.subscription_plan === 'free' || profile.payment_verified;

      if (!isExpired && paymentOk) {
        isAccountActive = true;
      } else if (isExpired) {
        reason = 'account_expired';
      } else if (!paymentOk) {
        reason = 'payment_not_verified';
      }
    }

    // Si la cuenta NO está activa y el usuario intenta acceder a una ruta protegida...
    if (!isAccountActive && !ALLOWED_ROUTES_WITHOUT_ACTIVE_PROFILE.some(path => request.nextUrl.pathname.startsWith(path))) {
      // ...lo redirigimos a su perfil para que vea el problema y pueda solucionarlo.
      const url = request.nextUrl.clone();
      url.pathname = '/profile';
      url.searchParams.set('reason', reason);
      return NextResponse.redirect(url);
    }

  } else {
    // Si NO hay usuario y la ruta no es pública, redirigir a login
    if (!ALLOWED_ROUTES_WITHOUT_ACTIVE_PROFILE.some(path => request.nextUrl.pathname.startsWith(path))) {
      const url = request.nextUrl.clone();
      url.pathname = '/login';
      return NextResponse.redirect(url);
    }
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
// Middleware robusto de seguridad para OposiAI

import { createServerClient } from '@supabase/ssr';
import { NextResponse, type NextRequest } from 'next/server';
import {
  PLAN_RESTRICTED_ROUTES,
  PUBLIC_ROUTES,
  AUTHENTICATED_ROUTES,
  TIMEOUTS,
  SECURITY_CONFIG
} from '@/config';

// Configuración de rutas y permisos
const ROUTE_PERMISSIONS = {
  // Rutas públicas (no requieren autenticación)
  public: PUBLIC_ROUTES,

  // Rutas que requieren autenticación básica
  authenticated: AUTHENTICATED_ROUTES,

  // Rutas que requieren planes específicos
  planRestricted: PLAN_RESTRICTED_ROUTES
};

// Configuración de seguridad (usando constantes centralizadas)
const MIDDLEWARE_SECURITY_CONFIG = {
  enableStrictValidation: SECURITY_CONFIG.ENABLE_STRICT_VALIDATION,
  requirePaymentVerification: SECURITY_CONFIG.REQUIRE_PAYMENT_VERIFICATION,
  enableAccessLogging: SECURITY_CONFIG.ENABLE_ACCESS_LOGGING,
  sessionTimeout: TIMEOUTS.SESSION_TIMEOUT_MS,
};

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  try {
    let supabaseResponse = NextResponse.next({
      request,
    });

    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        auth: {
          persistSession: false,
          autoRefreshToken: false,
          detectSessionInUrl: true // Habilitar para detectar tokens de recuperación
        },
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(cookiesToSet) {
            try {
              const filteredCookies = cookiesToSet.filter(cookie =>
                !cookie.name.includes('auth-token') &&
                !cookie.name.includes('refresh-token')
              );

              filteredCookies.forEach(({ name, value, options }) => {
                try {
                  request.cookies.set(name, value);
                } catch (cookieError) {
                  console.warn(`[MIDDLEWARE] Failed to set request cookie ${name}:`, cookieError);
                }
              });

              supabaseResponse = NextResponse.next({
                request,
              });

              filteredCookies.forEach(({ name, value, options }) => {
                try {
                  supabaseResponse.cookies.set(name, value, {
                    ...options,
                    maxAge: undefined,
                    expires: undefined
                  });
                } catch (cookieError) {
                  console.warn(`[MIDDLEWARE] Failed to set response cookie ${name}:`, cookieError);
                }
              });
            } catch (error) {
              console.warn('[MIDDLEWARE] Error in setAll cookies:', error);
            }
          },
        },
      }
    );

    // --- Obtener Usuario con manejo de errores mejorado ---
    let user = null;
    let authError = null;

    try {
      const authResult = await supabase.auth.getUser();
      user = authResult.data?.user || null;
      authError = authResult.error;
    } catch (fetchError: any) {
      console.warn('[MIDDLEWARE] Supabase auth fetch failed:', {
        error: fetchError.message,
        pathname,
        hasAuthCookie: !!request.cookies.get('sb-fxnhpxjijinfuxxxplzj-auth-token')
      });

      // Si es una ruta pública, continuar sin autenticación
      if (isPublicRoute(pathname)) {
        return addSecurityHeaders(supabaseResponse);
      }

      // Para rutas protegidas, redirigir a login
      return redirectToLogin(request);
    }

    // --- Lógica de Rutas Públicas ---
    if (isPublicRoute(pathname)) {
      // Si hay un usuario y está en una ruta pública como /login, redirigir
      if (user && (pathname === '/' || pathname === '/login')) {
        return NextResponse.redirect(new URL('/app', request.url));
      }
      return addSecurityHeaders(supabaseResponse);
    }

    // --- Rutas de autenticación especiales ---
    if (pathname === '/auth/reset-password') {
      return addSecurityHeaders(supabaseResponse);
    }

    const authSetupRoutes = ['/auth/callback', '/welcome', '/upgrade-plan'];
    if (user && authSetupRoutes.some(route => pathname.startsWith(route))) {
      // Para rutas de configuración, verificar si es un usuario legítimo
      const isLegitimateUser = await checkIfLegitimateUser(user, supabase);
      if (isLegitimateUser.isLegitimate) {
        return addSecurityHeaders(supabaseResponse);
      } else {
        return redirectToPayment(request, 'Invalid account - please complete registration');
      }
    }

    // --- Si NO hay Usuario y NO es ruta pública ---
    if (!user) {
      return redirectToLogin(request);
    }

    // --- Resto de las validaciones para rutas autenticadas ---
    if (isAuthenticatedRoute(pathname)) {
      const profileValidation = await validateUserProfile(user.id, supabase);

      if (!profileValidation.valid) {
        // Manejar usuarios autenticados sin perfil
        if (profileValidation.reason === 'Profile not found') {
          // Verificar si es un usuario que se registró para cuenta gratuita
          const isLegitimateUser = await checkIfLegitimateUser(user, supabase);

          if (isLegitimateUser.isLegitimate) {
            try {
              const accountType = isLegitimateUser.accountType === 'unknown' ? 'free' : isLegitimateUser.accountType;
              await createRecoveryProfile(user.id, accountType, supabase);
              // Continuar con la ejecución normal
            } catch (error) {
              return redirectToPayment(request, 'Profile recovery failed');
            }
          } else {
            return redirectToPayment(request, 'Invalid account - please complete registration');
          }
        } else {
          // Para otros errores de validación (cuenta expirada, pago no verificado, etc.)
          return redirectToPayment(request, profileValidation.reason);
        }
      }
    }

    // Para rutas con restricciones de plan
    if (isPlanRestrictedRoute(pathname)) {
      const planValidation = await validatePlanAccess(user.id, pathname, supabase);

      if (!planValidation.valid) {
        return redirectToUnauthorized(request, planValidation);
      }
    }

    // Agregar headers de seguridad y retornar
    return addSecurityHeaders(supabaseResponse);

  } catch (error: any) {
    console.error('❌ [MIDDLEWARE] Critical error:', {
      error: error.message,
      pathname,
      stack: error.stack,
      type: error.constructor.name
    });

    // Si es una ruta pública, permitir acceso con headers de seguridad
    if (isPublicRoute(pathname)) {
      console.warn('[MIDDLEWARE] Error en ruta pública, permitiendo acceso');
      return addSecurityHeaders(NextResponse.next({ request }));
    }

    // En caso de error crítico en rutas protegidas, denegar acceso por seguridad
    return redirectToLogin(request);
  }
}

// Funciones auxiliares para validación de rutas
function isPublicRoute(pathname: string): boolean {
  return ROUTE_PERMISSIONS.public.some(path =>
    pathname === path || pathname.startsWith(path + '/')
  );
}

function isAuthenticatedRoute(pathname: string): boolean {
  return ROUTE_PERMISSIONS.authenticated.some(path =>
    pathname.startsWith(path)
  );
}

function isPlanRestrictedRoute(pathname: string): boolean {
  return Object.keys(ROUTE_PERMISSIONS.planRestricted).some(path =>
    pathname.startsWith(path)
  );
}

// Funciones de validación
async function checkIfLegitimateUser(user: any, supabase: any): Promise<{
  isLegitimate: boolean;
  accountType: 'free' | 'paid' | 'unknown';
  reason?: string;
}> {
  try {
    // Verificar si el usuario tiene metadata que indique registro legítimo
    const userMetadata = user.user_metadata || {};
    const appMetadata = user.app_metadata || {};

    console.log(`🔍 [MIDDLEWARE] Checking user legitimacy for ${user.id}:`, {
      userMetadata: Object.keys(userMetadata),
      appMetadata: Object.keys(appMetadata),
      created_via: userMetadata.created_via,
      free_account: userMetadata.free_account,
      plan: userMetadata.plan
    });

    // Verificar si es una cuenta gratuita creada automáticamente
    if (userMetadata.created_via === 'free_registration' || userMetadata.free_account) {
      console.log(`✅ [MIDDLEWARE] User ${user.id} is legitimate: Free account registration`);
      return { isLegitimate: true, accountType: 'free', reason: 'Free account registration' };
    }

    // Verificar si es un usuario en proceso de configuración de contraseña
    if (userMetadata.plan && (userMetadata.plan === 'free' || userMetadata.plan === 'usuario' || userMetadata.plan === 'pro')) {
      const accountType = userMetadata.plan === 'free' ? 'free' : 'paid';
      console.log(`✅ [MIDDLEWARE] User ${user.id} is legitimate: User in setup process (${userMetadata.plan})`);
      return { isLegitimate: true, accountType, reason: 'User in setup process' };
    }

    // Verificar si tiene datos de Stripe en metadata (usuario de pago en proceso)
    if (userMetadata.stripe_session_id || userMetadata.stripe_customer_id) {
      console.log(`✅ [MIDDLEWARE] User ${user.id} is legitimate: Has Stripe metadata`);
      return { isLegitimate: true, accountType: 'paid', reason: 'Has Stripe metadata' };
    }

    // Verificar si hay transacciones de Stripe asociadas
    try {
      const { data: transactions, error } = await supabase
        .from('stripe_transactions')
        .select('id, status, plan_id')
        .eq('user_id', user.id)
        .limit(1);

      if (!error && transactions && transactions.length > 0) {
        console.log(`✅ [MIDDLEWARE] User ${user.id} is legitimate: Has payment transactions`);
        return { isLegitimate: true, accountType: 'paid', reason: 'Has payment transactions' };
      }
    } catch (dbError: any) {
      console.warn(`[MIDDLEWARE] Error checking transactions for user ${user.id}:`, dbError.message);
      // Continuar con otras verificaciones en lugar de fallar
    }

    // Verificar si el email está en la lista de usuarios invitados/creados por admin
    if (appMetadata.created_by_admin || userMetadata.invited_by_admin) {
      console.log(`✅ [MIDDLEWARE] User ${user.id} is legitimate: Admin created account`);
      return { isLegitimate: true, accountType: 'free', reason: 'Admin created account' };
    }

    // Si llegamos aquí, es sospechoso
    console.log(`🚨 [MIDDLEWARE] User ${user.id} is NOT legitimate: No legitimate registration method found`);
    return {
      isLegitimate: false,
      accountType: 'unknown',
      reason: 'No legitimate registration method found'
    };

  } catch (error) {
    console.error('❌ [MIDDLEWARE] Error checking user legitimacy:', error);
    return { isLegitimate: false, accountType: 'unknown', reason: 'Verification error' };
  }
}

async function createRecoveryProfile(
  userId: string,
  accountType: 'free' | 'paid',
  supabase: any
): Promise<void> {
  try {
    const currentMonth = new Date().toISOString().slice(0, 7) + '-01';

    let profileData;

    if (accountType === 'free') {
      // Crear perfil gratuito con expiración
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + 5); // 5 días

      profileData = {
        user_id: userId,
        subscription_plan: 'free',
        monthly_token_limit: 50000,
        current_month_tokens: 0,
        current_month: currentMonth,
        payment_verified: true, // Las cuentas gratuitas se consideran verificadas
        plan_expires_at: expirationDate.toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        security_flags: {
          recovered_profile: true,
          recovery_date: new Date().toISOString(),
          original_account_type: 'free'
        }
      };
    } else {
      // Para cuentas de pago, crear perfil básico sin verificación
      profileData = {
        user_id: userId,
        subscription_plan: 'free', // Temporal hasta que se verifique el pago
        monthly_token_limit: 50000,
        current_month_tokens: 0,
        current_month: currentMonth,
        payment_verified: false, // Requiere verificación manual
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        security_flags: {
          recovered_profile: true,
          recovery_date: new Date().toISOString(),
          original_account_type: 'paid',
          requires_manual_verification: true
        }
      };
    }

    const { error } = await supabase
      .from('user_profiles')
      .insert([profileData]);

    if (error) {
      console.error('[MIDDLEWARE] Error creating recovery profile:', {
        userId,
        accountType,
        error: error.message,
        code: error.code
      });
      throw new Error(`Failed to create recovery profile: ${error.message}`);
    }

    console.log(`✅ [MIDDLEWARE] Recovery profile created for user ${userId} (${accountType})`);
  } catch (error: any) {
    console.error('[MIDDLEWARE] Failed to create recovery profile:', {
      userId,
      accountType,
      error: error.message
    });
    throw error;
  }
}

async function validateUserProfile(userId: string, supabase: any): Promise<{
  valid: boolean;
  reason?: string;
}> {
  try {
    const { data: profile, error } = await supabase
      .from('user_profiles')
      .select('subscription_plan, payment_verified, plan_expires_at, auto_renew, security_flags')
      .eq('user_id', userId)
      .single();

    if (error) {
      console.warn('[MIDDLEWARE] Error fetching user profile:', {
        userId,
        error: error.message,
        code: error.code
      });
      return { valid: false, reason: 'Profile not found' };
    }

    if (!profile) {
      console.warn('[MIDDLEWARE] No profile found for user:', userId);
      return { valid: false, reason: 'Profile not found' };
    }

    // Verificar expiración para todos los planes que tengan plan_expires_at
    if (profile.plan_expires_at) {
      const now = new Date();
      const expiresAt = new Date(profile.plan_expires_at);

      if (now > expiresAt) {
        // Determinar el motivo de expiración basado en el plan
        let reason = 'Account expired';
        if (profile.subscription_plan === 'free') {
          reason = 'Free account expired';
        } else {
          reason = 'Subscription grace period expired';
        }

        return { valid: false, reason };
      }
    }

    // Verificar pago para planes de pago
    if (SECURITY_CONFIG.REQUIRE_PAYMENT_VERIFICATION &&
        profile.subscription_plan !== 'free' &&
        !profile.payment_verified) {
      return { valid: false, reason: 'Payment not verified' };
    }

    return { valid: true };
  } catch (error) {
    console.error('Error validating user profile:', error);
    return { valid: false, reason: 'Validation error' };
  }
}

async function validatePlanAccess(userId: string, pathname: string, supabase: any): Promise<{
  valid: boolean;
  reason?: string;
  requiredPlans?: string[];
}> {
  try {
    // Encontrar qué planes se requieren para esta ruta
    const requiredPlans = Object.entries(ROUTE_PERMISSIONS.planRestricted)
      .find(([path]) => pathname.startsWith(path))?.[1];

    if (!requiredPlans) {
      return { valid: true };
    }

    const { data: profile, error } = await supabase
      .from('user_profiles')
      .select('subscription_plan, payment_verified')
      .eq('user_id', userId)
      .single();

    if (error || !profile) {
      return {
        valid: false,
        reason: 'Profile not found',
        requiredPlans
      };
    }

    // Verificar si el plan del usuario está en la lista de planes permitidos
    if (!requiredPlans.includes(profile.subscription_plan)) {
      return {
        valid: false,
        reason: `Plan ${profile.subscription_plan} not sufficient`,
        requiredPlans
      };
    }

    return { valid: true };
  } catch (error) {
    console.error('Error validating plan access:', error);
    return {
      valid: false,
      reason: 'Validation error',
      requiredPlans: []
    };
  }
}

// Funciones de redirección
function redirectToLogin(request: NextRequest): NextResponse {
  const url = request.nextUrl.clone();
  url.pathname = '/login';
  url.searchParams.set('redirect', request.nextUrl.pathname);
  return NextResponse.redirect(url);
}

function redirectToPayment(request: NextRequest, reason?: string): NextResponse {
  const url = request.nextUrl.clone();
  url.pathname = '/payment';
  if (reason) {
    url.searchParams.set('reason', reason);
  }
  return NextResponse.redirect(url);
}

function redirectToUnauthorized(request: NextRequest, validation: any): NextResponse {
  const url = request.nextUrl.clone();
  url.pathname = '/auth/unauthorized';
  url.searchParams.set('reason', validation.reason || 'Access denied');
  url.searchParams.set('feature', request.nextUrl.pathname);
  if (validation.requiredPlans) {
    url.searchParams.set('required_plan', validation.requiredPlans.join(','));
  }
  return NextResponse.redirect(url);
}

// Función para agregar headers de seguridad
function addSecurityHeaders(response: NextResponse): NextResponse {
  // Headers de seguridad
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  // CSP básico
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com https://d3js.org; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://*.supabase.co https://api.stripe.com;"
  );

  return response;
}

// Configuración del matcher para definir en qué rutas se ejecutará el middleware.
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     *
     * También puedes excluir rutas API específicas aquí si prefieres no hacerlo en el código del middleware,
     * pero manejarlo en el código da más flexibilidad si algunas rutas API necesitan auth y otras no.
     * Si todas las rutas /api/* están protegidas o no, puedes gestionarlo arriba.
     *
     * La expresión regular abajo intenta cubrir los casos más comunes:
     */
    '/((?!_next/static|_next/image|favicon.ico|manifest.json|robots.txt|.*\\..*).*)',
    // Explicación de la regex mejorada:
    // /((?!             // Inicio de grupo de no coincidencia (negative lookahead)
    // _next/static      // No coincidir con _next/static
    // |_next/image      // O no coincidir con _next/image
    // |favicon.ico     // O no coincidir con favicon.ico
    // |manifest.json   // O no coincidir con manifest.json (común para PWAs)
    // |robots.txt      // O no coincidir con robots.txt
    // |.*\\..*         // O no coincidir con cualquier cosa que contenga un punto (archivos como .png, .css, etc.)
    // ).*)             // Fin del lookahead, coincide con cualquier otra cosa
  ],
};